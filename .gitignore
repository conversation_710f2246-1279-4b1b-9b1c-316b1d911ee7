# Node modules
node_modules/

# Build outputs
dist/
build/
tmp/
temp/
.cache/
.nyc_output/

# AWS Lambda zip packages
*.zip

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.*.local

# OS-specific files
.DS_Store
Thumbs.db

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo

# TypeScript
*.tsbuildinfo

# Jest test coverage
coverage/

# Optional: Ignore all generated reports if you're storing them locally
BalanceSheet/output/
ProfitLoss/output/
profitandloss/output/
TrialBalance/output/
