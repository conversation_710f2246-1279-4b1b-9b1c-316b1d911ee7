/**
 * Xero Balance Sheet Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Balance Sheet data from Xero API with intelligent
 * sync strategy based on existing data. It implements dual storage architecture:
 *
 * Storage Tables:
 * - BalanceSheetTracking: Detailed data WITH tracking categories (dimensional reporting)
 * - BalanceSheet: Aggregated data WITHOUT tracking categories (summary reporting)
 *
 * Sync Strategy:
 * - Initial Sync: 5 years (60 months) of historical data when no data exists
 * - Regular Sync: 13 months (financial year + 1 month) for ongoing updates
 *
 * Key Features:
 * - Intelligent sync period determination based on existing data
 * - Strict rate limiting (1 concurrent for initial, 2 for regular sync)
 * - Dual API calls per month (with/without tracking categories)
 * - Comprehensive error handling and retry logic
 * - Database transaction safety with cleanup on failure
 *
 * Rate Limiting Strategy:
 * - Xero API: 60 requests/minute, 5000/day per app
 * - Initial sync: 1 concurrent request (conservative for large datasets)
 * - Regular sync: 2 concurrent requests (balanced performance)
 * - 1-second delay between dual API calls per month
 *
 * <AUTHOR> Sheet Sync Service
 * @version 2.0.0
 * @since 2024-01-01
 */

import {
    APIGatewayProxyEvent,
    Context,
    SQSEvent,
    APIGatewayProxyResult,
} from 'aws-lambda';
import { XeroRequestData, ValidationError } from '../types';
import { processBalanceSheetRequest } from '../services/balanceSheetService';
import { parseRequestData } from '../utils/requestParser';

/**
 * AWS Lambda Handler for Balance Sheet Synchronization
 *
 * Handles both API Gateway and SQS events for Balance Sheet data synchronization.
 * Supports both direct API calls and asynchronous queue processing.
 *
 * Event Types:
 * - API Gateway: Direct synchronous processing with HTTP response
 * - SQS: Asynchronous batch processing from queue messages
 *
 * @param {APIGatewayProxyEvent | SQSEvent} event - AWS event (API Gateway or SQS)
 * @param {Context} context - AWS Lambda execution context
 * @returns {Promise<APIGatewayProxyResult | void>} HTTP response for API Gateway, void for SQS
 */
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    // Set Lambda timeout warning at 80% of configured timeout
    const timeoutWarning = setTimeout(() => {
        console.warn('⚠️ Lambda approaching timeout, consider increasing timeout or reducing batch size');
    }, (context.getRemainingTimeInMillis() * 0.8));

    try {
        if ('Records' in event && event.Records?.length) {
            // SQS Event Processing
            console.log(`📥 Processing ${event.Records.length} SQS messages`);

            for (const record of event.Records) {
                try {
                    const requestData = JSON.parse(record.body) as XeroRequestData;
                    console.log(`🔄 Processing Balance Sheet sync for company: ${requestData.companyId}`);

                    await processBalanceSheetRequest(requestData, context, "SYSTEM");

                    console.log(`✅ Successfully processed Balance Sheet sync for company: ${requestData.companyId}`);
                } catch (error: any) {
                    console.error(`❌ Failed to process SQS message for company: ${JSON.parse(record.body).companyId}`, {
                        error: error.message,
                        stack: error.stack,
                        messageId: record.messageId
                    });
                    throw error; // Trigger Lambda retry mechanism
                }
            }

            console.log(`🎉 Successfully processed all ${event.Records.length} SQS messages`);
            return;
        } else {
            // API Gateway Event Processing
            console.log('🌐 Processing API Gateway request');

            try {
                const requestData = parseRequestData(event);
                console.log(`🔄 Starting Balance Sheet sync for company: ${requestData.companyId}`);

                const startTime = Date.now();
                await processBalanceSheetRequest(requestData, context, "USER");
                const duration = Date.now() - startTime;

                console.log(`✅ Balance Sheet sync completed in ${duration}ms for company: ${requestData.companyId}`);

                return {
                    statusCode: 200,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Processing-Time': duration.toString()
                    },
                    body: JSON.stringify({
                        success: true,
                        message: '✅ Balance Sheet data synchronized successfully',
                        timestamp: new Date().toISOString(),
                        processingTimeMs: duration,
                        companyId: requestData.companyId
                    }),
                };
            } catch (error: any) {
                console.error('❌ Balance Sheet sync failed:', {
                    error: error.message,
                    stack: error.stack,
                    type: error.constructor.name
                });

                const statusCode = error instanceof ValidationError ? 400 : 500;

                return {
                    statusCode,
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        success: false,
                        error: error.message,
                        type: error.constructor.name,
                        timestamp: new Date().toISOString(),
                        ...(statusCode === 400 && {
                            details: 'Please check your request parameters and try again'
                        })
                    }),
                };
            }
        }
    } finally {
        clearTimeout(timeoutWarning);
    }
};
