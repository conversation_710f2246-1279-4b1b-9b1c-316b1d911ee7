/**
 * Trial Balance Data Extraction Service
 * 
 * Extracts and processes Trial Balance data from Xero API responses
 */

import {
    XeroTrialBalanceReport,
    XeroTrialBalanceRow,
    ProcessedTrialBalanceData,
    XeroRequestData
} from '../types';

/**
 * Extract Trial Balance data from Xero API response
 * 
 * @param report - Xero Trial Balance report data
 * @param requestData - Original request data
 * @param month - Month information for processing
 * @returns Array of processed Trial Balance data
 */
export function extractTrialBalanceData(
    report: XeroTrialBalanceReport,
    requestData: XeroRequestData,
    month: { year: number; month: number }
): ProcessedTrialBalanceData[] {
    console.log(`🔄 Extracting Trial Balance data for ${month.year}-${month.month}`);

    if (!report.Rows || !Array.isArray(report.Rows)) {
        console.warn('⚠️ No rows found in Trial Balance report');
        return [];
    }

    const results: ProcessedTrialBalanceData[] = [];

    // Process each row in the report
    for (const row of report.Rows) {
        if (row.RowType === 'Section' && row.Rows) {
            // Process section rows (account groups)
            for (const innerRow of row.Rows) {
                const processedData = processTrialBalanceRow(innerRow, requestData, month);
                if (processedData) {
                    results.push(processedData);
                }
            }
        } else if (row.RowType === 'Row') {
            // Process direct rows
            const processedData = processTrialBalanceRow(row, requestData, month);
            if (processedData) {
                results.push(processedData);
            }
        }
    }

    console.log(`✅ Extracted ${results.length} Trial Balance records for ${month.year}-${month.month}`);
    return results;
}

/**
 * Process individual Trial Balance row
 * 
 * @param row - Xero Trial Balance row
 * @param requestData - Original request data
 * @param month - Month information
 * @returns Processed Trial Balance data or null if invalid
 */
function processTrialBalanceRow(
    row: XeroTrialBalanceRow,
    requestData: XeroRequestData,
    month: { year: number; month: number }
): ProcessedTrialBalanceData | null {
    if (row.RowType !== 'Row' || !row.Cells || row.Cells.length < 5) {
        return null;
    }

    try {
        // Extract account information from first cell
        const accountCell = row.Cells[0];
        const accountName = accountCell?.Value || null;

        if (!accountName || accountName.trim() === '') {
            return null;
        }

        // Extract account ID from attributes
        let accountId = null;
        if (accountCell?.Attributes) {
            const accountAttr = accountCell.Attributes.find(attr => attr.Id === 'account');
            accountId = accountAttr?.Value || null;
        }

        // If no account ID found, skip this row
        if (!accountId) {
            console.log(`⚠️ Skipping row without account ID: ${accountName}`);
            return null;
        }

        // Extract amounts from cells
        // Trial Balance typically has: Account, Debit, Credit, Net Movement, Month End Balance
        const netChangeAmount = parseAmount(row.Cells[3]?.Value) || 0;
        const monthEndBalance = parseAmount(row.Cells[4]?.Value) || 0;

        // Note: Period debit and credit amounts are in columns 1 and 2 but not used in current schema

        // Calculate month end debit and credit amounts
        // If month end balance is positive, it's typically a debit balance
        // If negative, it's typically a credit balance
        const monthEndDebitAmount = monthEndBalance >= 0 ? Math.abs(monthEndBalance) : 0;
        const monthEndCreditAmount = monthEndBalance < 0 ? Math.abs(monthEndBalance) : 0;

        const processedData: ProcessedTrialBalanceData = {
            Year: month.year,
            Month: month.month,
            AccountId: accountId,
            AccountName: accountName.trim(),
            Amount: monthEndBalance, // Main balance amount
            CompanyId: requestData.companyId,
            monthEndDebitAmount: monthEndDebitAmount,
            monthEndCreditAmount: monthEndCreditAmount,
            netChangeAmount: netChangeAmount,
        };

        return processedData;

    } catch (error) {
        console.error('❌ Error processing Trial Balance row:', {
            error: error instanceof Error ? error.message : 'Unknown error',
            rowData: row,
        });
        return null;
    }
}

/**
 * Parse amount string to number
 * 
 * @param amountString - Amount string from Xero API
 * @returns Parsed number or null if invalid
 */
function parseAmount(amountString: string | undefined): number | null {
    if (!amountString || amountString.trim() === '') {
        return null;
    }

    // Remove commas and handle negative values in parentheses
    let cleanAmount = amountString.replace(/,/g, '').trim();

    // Handle negative amounts in parentheses format: (1,234.56)
    if (cleanAmount.startsWith('(') && cleanAmount.endsWith(')')) {
        cleanAmount = '-' + cleanAmount.slice(1, -1);
    }

    const parsed = parseFloat(cleanAmount);
    return isNaN(parsed) ? null : parsed;
}

/**
 * Validate Trial Balance data before processing
 * 
 * @param report - Xero Trial Balance report
 * @returns true if valid, false otherwise
 */
export function validateTrialBalanceReport(report: any): report is XeroTrialBalanceReport {
    if (!report) {
        console.error('❌ Trial Balance report is null or undefined');
        return false;
    }

    if (!report.ReportName || !report.ReportName.toLowerCase().includes('trial')) {
        console.error('❌ Invalid report type, expected Trial Balance report');
        return false;
    }

    if (!Array.isArray(report.Rows)) {
        console.error('❌ Trial Balance report missing Rows array');
        return false;
    }

    return true;
}
