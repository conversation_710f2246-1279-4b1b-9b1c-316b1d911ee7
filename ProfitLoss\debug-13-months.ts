import moment from 'moment';

// Debug function to check 13-month logic with different financial year ends
function debug13MonthLogic() {
    console.log("🔍 Debugging 13-Month Logic\n");

    // Test with common financial year ends
    const testDates = [
        "2025-03-31", // March year end
        "2024-06-30", // June year end
        "2024-12-31", // December year end
    ];

    testDates.forEach((fyEndDate, index) => {
        console.log(`${index + 1}. Testing Financial Year End: ${fyEndDate}`);
        console.log("-".repeat(50));

        const financialYearEnd = moment(fyEndDate).endOf("month");
        console.log(`Financial Year End: ${financialYearEnd.format('YYYY-MM-DD')}`);

        // Calculate financial year start (12 months before financial year end)
        const financialYearStart = financialYearEnd.clone().subtract(12, "months").startOf("month");
        console.log(`Financial Year Start: ${financialYearStart.format('YYYY-MM-DD')}`);

        // Generate 13 months starting from financial year start
        const monthRanges = [];
        for (let i = 0; i < 13; i++) {
            const monthDate = financialYearStart.clone().add(i, "months");
            const startDate = monthDate.clone().startOf("month").format("YYYY-MM-DD");
            const endDate = monthDate.clone().endOf("month").format("YYYY-MM-DD");

            monthRanges.push({
                startDate,
                endDate,
                monthDate: monthDate.clone(),
                year: monthDate.year(),
                month: monthDate.month() + 1,
                displayName: monthDate.format("MMM YYYY")
            });
        }

        console.log(`Generated ${monthRanges.length} months:`);
        monthRanges.forEach((range, idx) => {
            console.log(`  ${idx + 1}. ${range.displayName} (${range.startDate} to ${range.endDate})`);
        });

        console.log(`\nRange: ${monthRanges[0]?.startDate} to ${monthRanges[monthRanges.length - 1]?.endDate}`);
        console.log(`Total months: ${monthRanges.length}\n`);
    });

    // Now let's check what might cause only 12 months to be processed
    console.log("🚨 Potential Issues That Could Cause Only 12 Months:");
    console.log("1. Loop condition: for (let i = 0; i < 13; i++) should generate 13 iterations");
    console.log("2. Financial year end date might be null/undefined");
    console.log("3. One month might be failing during API call or processing");
    console.log("4. Database insertion might be failing for one month");
    console.log("5. Date calculation might have an off-by-one error");

    console.log("\n🔧 Debugging Steps:");
    console.log("1. Check your Company.FinancialYearEnd value in database");
    console.log("2. Add console.log to see how many monthRanges are generated");
    console.log("3. Add console.log in the processing loop to see which months are processed");
    console.log("4. Check for any error messages during sync");
}

// Run the debug
debug13MonthLogic();
