import pLimit from 'p-limit';
import moment from 'moment';
import { XeroRequestData, ProcessedRowData } from '../types';

export interface ParallelProcessingOptions {
    concurrency?: number;
    batchSize?: number;
    retryAttempts?: number;
    retryDelay?: number;
    timeout?: number;
}

export interface ProcessingResult<T> {
    success: boolean;
    data?: T;
    error?: Error;
    duration: number;
    month: string;
    attempt: number;
}

export interface BatchResult<T> {
    results: ProcessingResult<T>[];
    totalDuration: number;
    successCount: number;
    errorCount: number;
    errors: Array<{ month: string; error: Error }>;
}

/**
 * Generate month ranges for parallel processing
 */
export function generateMonthRanges(
    startDate: string,
    endDate: string
): Array<{ startDate: string; endDate: string; month: string }> {
    const start = moment(startDate);
    const end = moment(endDate);
    const ranges: Array<{ startDate: string; endDate: string; month: string }> = [];

    let current = start.clone().startOf('month');

    while (current.isSameOrBefore(end, 'month')) {
        const monthStart = current.clone();
        const monthEnd = current.clone().endOf('month');

        // Adjust for the actual date range
        if (monthStart.isBefore(start)) {
            monthStart.set({
                date: start.date(),
                hour: start.hour(),
                minute: start.minute(),
                second: start.second()
            });
        }

        if (monthEnd.isAfter(end)) {
            monthEnd.set({
                date: end.date(),
                hour: end.hour(),
                minute: end.minute(),
                second: end.second()
            });
        }

        ranges.push({
            startDate: monthStart.format('YYYY-MM-DD'),
            endDate: monthEnd.format('YYYY-MM-DD'),
            month: current.format('YYYY-MM')
        });

        current.add(1, 'month');
    }

    return ranges;
}

/**
 * Process multiple months in parallel with concurrency control
 */
export async function processMonthsInParallel<T>(
    processor: (requestData: XeroRequestData) => Promise<T>,
    baseRequestData: XeroRequestData,
    options: ParallelProcessingOptions = {}
): Promise<BatchResult<T>> {
    const {
        concurrency = 3, // Conservative default for API rate limits
        retryAttempts = 2,
        retryDelay = 1000,
        timeout = 30000 // 30 seconds per month
    } = options;

    const monthRanges: any = generateMonthRanges(
        baseRequestData.startDate,
        baseRequestData.endDate
    );



    const limit = pLimit(concurrency);
    const startTime = Date.now();
    const results: ProcessingResult<T>[] = [];

    // Create processing tasks
    const tasks = monthRanges.map((range: any) =>
        limit(async () => {
            const monthRequestData: XeroRequestData = {
                ...baseRequestData,
                startDate: range.startDate,
                endDate: range.endDate
            };

            return processWithRetry(
                processor,
                monthRequestData,
                range.month,
                retryAttempts,
                retryDelay,
                timeout
            );
        })
    );

    // Execute all tasks
    const taskResults = await Promise.allSettled(tasks);

    // Process results
    taskResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
            results.push(result.value);
        } else {
            results.push({
                success: false,
                error: result.reason instanceof Error ? result.reason : new Error(String(result.reason)),
                duration: 0,
                month: monthRanges[index].month,
                attempt: 1
            });
        }
    });

    const totalDuration = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;
    const errors = results
        .filter(r => !r.success)
        .map(r => ({ month: r.month, error: r.error! }));



    return {
        results,
        totalDuration,
        successCount,
        errorCount,
        errors
    };
}

/**
 * Process a single month with retry logic
 */
async function processWithRetry<T>(
    processor: (requestData: XeroRequestData) => Promise<T>,
    requestData: XeroRequestData,
    month: string,
    maxAttempts: number,
    retryDelay: number,
    timeout: number
): Promise<ProcessingResult<T>> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        const startTime = Date.now();

        try {
            // Add timeout to the processing
            const result = await Promise.race([
                processor(requestData),
                new Promise<never>((_, reject) =>
                    setTimeout(() => reject(new Error(`Timeout after ${timeout}ms`)), timeout)
                )
            ]);

            const duration = Date.now() - startTime;


            return {
                success: true,
                data: result,
                duration,
                month,
                attempt
            };
        } catch (error) {
            lastError = error instanceof Error ? error : new Error(String(error));
            const duration = Date.now() - startTime;

            if (attempt === maxAttempts) {
                console.error(`❌ Month ${month} failed after ${maxAttempts} attempts: ${lastError.message}`);
                return {
                    success: false,
                    error: lastError,
                    duration,
                    month,
                    attempt
                };
            }

            console.warn(`⚠️ Month ${month} failed (attempt ${attempt}/${maxAttempts}): ${lastError.message}`);

            // Wait before retry with exponential backoff
            const delay = retryDelay * Math.pow(2, attempt - 1);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    // This should never be reached, but TypeScript requires it
    return {
        success: false,
        error: lastError || new Error('Unknown error'),
        duration: 0,
        month,
        attempt: maxAttempts
    };
}

/**
 * Combine results from multiple months
 */
export function combineMonthResults(
    results: ProcessingResult<ProcessedRowData[]>[]
): ProcessedRowData[] {
    const combinedData: ProcessedRowData[] = [];

    results
        .filter(result => result.success && result.data)
        .forEach(result => {
            if (result.data) {
                combinedData.push(...result.data);
            }
        });

    // Sort by date for consistency
    combinedData.sort((a: any, b: any) => {
        const dateA = new Date(a.date || '');
        const dateB = new Date(b.date || '');
        return dateA.getTime() - dateB.getTime();
    });

    return combinedData;
}

/**
 * Process months in batches for very large date ranges
 */
export async function processBatchedMonths<T>(
    processor: (requestData: XeroRequestData) => Promise<T>,
    baseRequestData: XeroRequestData,
    options: ParallelProcessingOptions = {}
): Promise<BatchResult<T>> {
    const { batchSize = 6 } = options; // Process 6 months at a time

    const monthRanges = generateMonthRanges(
        baseRequestData.startDate,
        baseRequestData.endDate
    );

    if (monthRanges.length <= batchSize) {
        // If we have fewer months than batch size, process normally
        return processMonthsInParallel(processor, baseRequestData, options);
    }



    const allResults: ProcessingResult<T>[] = [];
    const allErrors: Array<{ month: string; error: Error }> = [];
    let totalDuration = 0;

    // Process in batches
    for (let i = 0; i < monthRanges.length; i += batchSize) {
        const batch: any = monthRanges.slice(i, i + batchSize);
        const batchStart = batch[0].startDate;
        const batchEnd = batch[batch.length - 1].endDate;



        const batchRequestData: XeroRequestData = {
            ...baseRequestData,
            startDate: batchStart,
            endDate: batchEnd
        };

        const batchResult = await processMonthsInParallel(
            processor,
            batchRequestData,
            { ...options, concurrency: Math.min(options.concurrency || 3, batch.length) }
        );

        allResults.push(...batchResult.results);
        allErrors.push(...batchResult.errors);
        totalDuration += batchResult.totalDuration;

        // Small delay between batches to be respectful to APIs
        if (i + batchSize < monthRanges.length) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    const successCount = allResults.filter(r => r.success).length;
    const errorCount = allResults.filter(r => !r.success).length;

    return {
        results: allResults,
        totalDuration,
        successCount,
        errorCount,
        errors: allErrors
    };
}
