# Trial Balance Lambda Function

AWS Lambda function for synchronizing Trial Balance data from Xero API to PostgreSQL database.

## Overview

This Lambda function provides automated synchronization of Trial Balance data from Xero accounting software. It supports both initial bulk synchronization and ongoing incremental updates with intelligent sync strategies.

## Features

- **Intelligent Sync Strategy**: Automatically determines sync period based on existing data
- **Rate Limiting**: Conservative API rate limiting to respect Xero API limits
- **Error Handling**: Comprehensive error handling with detailed logging
- **Token Management**: Automatic Xero OAuth token refresh
- **Transaction Safety**: Database operations wrapped in transactions
- **Dual Event Support**: Handles both API Gateway and SQS events

## Architecture

### Sync Strategies

- **Initial Sync**: 60 months (5 years) of historical data when no existing data
- **Regular Sync**: 13 months (current financial year + 1 month buffer)

### Concurrency Limits

- **Initial Sync**: 1 concurrent request (conservative for large data sets)
- **Regular Sync**: 2 concurrent requests (balanced performance)

## Database Schema

The function stores data in the `TrialBalance` table with the following structure:

```sql
model TrialBalance {
  Id                   String  @id @default(uuid()) @db.Uuid
  Year                 Int
  Month                Int
  AccountId            String
  AccountName          String
  Amount               Decimal @db.Decimal(18, 2)
  CompanyId            String  @db.Uuid
  monthEndDebitAmount  Decimal @db.Decimal(15, 2)
  monthEndCreditAmount Decimal @db.Decimal(15, 2)
  netChangeAmount      Decimal @db.Decimal(15, 2)

  Company Company @relation(fields: [CompanyId], references: [Id])
}
```

## Environment Variables

Required environment variables:

```bash
DATABASE_URL=postgresql://user:password@host:port/database
XERO_CLIENT_ID=your_xero_client_id
XERO_CLIENT_SECRET=your_xero_client_secret
XERO_REDIRECT_URI=your_xero_redirect_uri
XERO_BASE_URL=https://api.xero.com/api.xro/2.0/ # Optional, defaults to production
```

## API Usage

### API Gateway Endpoint

**POST** `/trial-balance/sync`

Request body:
```json
{
  "companyId": "uuid-of-company",
  "startDate": "2024-01-01",  // Optional
  "endDate": "2024-12-31"     // Optional
}
```

Response:
```json
{
  "success": true,
  "message": "✅ Trial Balance data synchronized successfully",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "processingTimeMs": 15000,
  "companyId": "uuid-of-company"
}
```

### SQS Queue

Queue message format:
```json
{
  "companyId": "uuid-of-company",
  "tenantId": "xero-tenant-id",  // Optional
  "startDate": "2024-01-01",     // Optional
  "endDate": "2024-12-31"        // Optional
}
```

## Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Build the project**:
   ```bash
   npm run build
   ```

3. **Deploy to AWS**:
   ```bash
   npm run deploy
   ```

## Development

### Local Development

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Run locally**:
   ```bash
   npm run start:local
   ```

### Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

### Linting

```bash
# Check for linting issues
npm run lint

# Fix linting issues
npm run lint:fix
```

## Deployment

### Development Environment

```bash
npm run deploy:dev
```

### Production Environment

```bash
npm run deploy:prod
```

## Monitoring

The function provides comprehensive logging for monitoring:

- **Success Logs**: Detailed processing information
- **Error Logs**: Comprehensive error details with context
- **Performance Metrics**: Processing times and data volumes
- **API Metrics**: Xero API call performance and rate limiting

## Error Handling

The function handles various error scenarios:

- **Authentication Errors**: Automatic token refresh
- **Rate Limiting**: Intelligent backoff and retry
- **Data Validation**: Comprehensive input validation
- **Network Issues**: Timeout and retry mechanisms
- **Database Errors**: Transaction rollback and error reporting

## Troubleshooting

### Common Issues

1. **Token Expired**: Function automatically refreshes tokens
2. **Rate Limiting**: Reduce concurrency or increase delays
3. **Missing Data**: Check Xero subscription and permissions
4. **Database Connection**: Verify DATABASE_URL and network access

### Logs

Check CloudWatch logs for detailed error information:
- Function execution logs
- API call traces
- Database operation logs
- Performance metrics

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
