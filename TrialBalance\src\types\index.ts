/**
 * Type definitions for Trial Balance Lambda function
 */

/**
 * Request data structure for Xero Trial Balance synchronization
 */
export interface XeroRequestData {
    companyId: string;
    tenantId?: string;
    startDate?: string;
    endDate?: string;
}

/**
 * Processed Trial Balance data structure for database storage
 */
export interface ProcessedTrialBalanceData {
    Year: number;
    Month: number;
    AccountId: string;
    AccountName: string;
    Amount: number;
    CompanyId: string;
    monthEndDebitAmount: number;
    monthEndCreditAmount: number;
    netChangeAmount: number;
}

/**
 * Xero API error structure
 */
export interface XeroError {
    Type: string;
    Title: string;
    Detail: string;
    ValidationErrors?: Array<{
        Message: string;
        Source: string;
    }>;
}

/**
 * Custom validation error
 */
export class ValidationError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'ValidationError';
    }
}

/**
 * Xero Trial Balance report structure
 */
export interface XeroTrialBalanceReport {
    ReportID: string;
    ReportName: string;
    ReportType: string;
    ReportTitles: string[];
    ReportDate: string;
    UpdatedDateUTC: string;
    Fields: any[];
    Rows: XeroTrialBalanceRow[];
}

/**
 * Xero Trial Balance row structure
 */
export interface XeroTrialBalanceRow {
    RowType: string;
    Cells: XeroTrialBalanceCell[];
    Title?: string;
    Rows?: XeroTrialBalanceRow[];
}

/**
 * Xero Trial Balance cell structure
 */
export interface XeroTrialBalanceCell {
    Value: string;
    Attributes?: Array<{
        Value: string;
        Id: string;
    }>;
}

/**
 * Month information for processing
 */
export interface MonthInfo {
    year: number;
    month: number;
    startDate: string;
    endDate: string;
}

/**
 * Production configuration for API calls with enhanced rate limiting
 */
export interface ProductionConfig {
    API_TIMEOUT_MS: number;
    MAX_CONCURRENT_REQUESTS: number;
    RETRY_ATTEMPTS: number;
    RETRY_DELAY_MS: number;
    // Enhanced rate limiting properties
    XERO_API_DELAY_MS: number;
    MAX_RETRY_DELAY_MS: number;
    EXPONENTIAL_BACKOFF_MULTIPLIER: number;
}

/**
 * Company integration data
 */
export interface CompanyIntegration {
    Id: string;
    XeroAccessToken: string | null;
    XeroTenantId: string | null;
    XeroTokenExpiry: Date | null;
    XeroRefreshToken: string | null;
    XeroRefreshTokenExpiry: Date | null;
}

/**
 * Sync period configuration
 */
export interface SyncPeriod {
    startDate: Date;
    endDate: Date;
    monthsToSync: number;
    isInitialSync: boolean;
}

/**
 * API Log entry for tracking API calls
 */
export interface ApiLogEntry {
    id: string;
    userId?: string;
    companyId: string;
    method: string;
    apiUrl: string;
    status: string;
    integrationName: string;
    duration: string;
    apiName: string;
    apiRequest?: any;
    apiResponse?: any;
    isActive: boolean;
    createdAt: Date;
}

/**
 * Sync Log entry for tracking sync processes
 */
export interface SyncLogEntry {
    id: string;
    requestId?: string;
    entity: string;
    integration: string;
    apiEndpoint?: string;
    method?: string;
    status: 'PENDING' | 'IN_PROGRESS' | 'SUCCESS' | 'WARNING' | 'ERROR' | 'RETRYING' | 'CANCELLED';
    message?: string;
    duration?: string;
    retryCount: number;
    maxRetries: number;
    lastRetryAt?: Date;
    nextRetryAt?: Date;
    startedAt: Date;
    completedAt?: Date;
    companyId: string;
    userId?: string;
    requestPayload?: any;
    responsePayload?: any;
    errorDetails?: any;
    createdAt: Date;
    updatedAt: Date;
}

export class XeroError extends Error {
    constructor(
        message: string,
        public statusCode?: number,
        public originalError?: any
    ) {
        super(message);
        this.name = 'XeroError';
    }
}
export class TokenRefreshError extends XeroError {
    constructor(message: string, originalError?: any) {
        super(message, 401, originalError);
        this.name = 'TokenRefreshError';
    }
}

export interface XeroTokenResponse {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
}