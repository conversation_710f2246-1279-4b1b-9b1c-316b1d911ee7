// Request/Response Types
export interface XeroRequestData {
  userId: string;
  companyId: string;
  startDate: string;
  endDate: string;

  dumpToDatabase?: boolean;
}

export interface XeroTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

// Xero API Response Types
export interface XeroReportHeader {
  Time: string;
  ReportName: string;
  Currency: string;
  ReportBasis?: string;
  StartPeriod?: string;
  EndPeriod?: string;
  SummarizeColumnsBy?: string;
  Option?: Array<{
    Name: string;
    Value: string;
  }>;
}

export interface XeroColumnMetaData {
  Name: string;
  Value: string;
}

export interface XeroColumn {
  ColTitle: string;
  ColType: string;
  MetaData?: XeroColumnMetaData[];
}

export interface XeroColumns {
  Column: XeroColumn[];
}

export interface XeroRowData {
  value: string;
  id?: string;
}

export interface XeroRow {
  ColData: XeroRowData[];
  group?: string;
}

export interface XeroRows {
  Row: XeroRow[];
}

export interface XeroProfitLossReport {
  Header: XeroReportHeader;
  Columns: XeroColumns;
  Rows: XeroRows;
}

export interface XeroApiResponse {
  QueryResponse?: {
    ProfitAndLoss: XeroProfitLossReport[];
  };
  ProfitAndLoss?: XeroProfitLossReport;
  // For direct report responses
  Header?: XeroReportHeader;
  Columns?: XeroColumns;
  Rows?: XeroRows;
}

// Internal Processing Types
export interface ColumnInfo {
  index: number;
  month: number;
  year: number;
}

export interface ProcessedRowData {
  Year: number;
  Month: number;
  AccountId: string;
  AccountName: string;
  Amount: number;
  TrackingCategoryId1?: string | null;
  TrackingCategoryId2?: string | null;
  CompanyId: string;
}

export interface ProcessedRowDataWithoutTracking {
  Year: number;
  Month: number;
  AccountId: string;
  AccountName: string;
  Amount: number;
  CompanyId: string;
}

export interface CategorizedData {
  headers: any[];
  dataRows: any[];
  summaries: any[];
  sections: any[];
}

export interface ProcessedXeroData {
  columns: any[];
  categorizedData: CategorizedData;
  allRowData: any[];
  metadata: any;
  statistics: {
    totalRows: number;
    headerCount: number;
    dataRowCount: number;
    summaryCount: number;
    sectionCount: number;
    maxNestingLevel: number;
  };
}

// Environment Variables Type
export interface EnvironmentConfig {
  DATABASE_URL: string;
  XERO_CLIENT_ID: string;
  XERO_CLIENT_SECRET: string;
  XERO_TOKEN_URL?: string;
  XERO_BASE_URL?: string;
  FIRST_RETRY?: string;
  SECOND_RETRY?: string;
  LAST_RETRY?: string;
  REGION?: string;
  ACCESS_KEY_ID?: string;
  SECRET_ACCESS_KEY?: string;
  IS_OFFLINE?: string;
  PRISMA_QUERY_ENGINE_LIBRARY?: string;
  AWS_LAMBDA_FUNCTION_NAME?: string;
}

// Custom Error Types
export class XeroError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'XeroError';
  }
}

export class TokenRefreshError extends XeroError {
  constructor(message: string, originalError?: any) {
    super(message, 401, originalError);
    this.name = 'TokenRefreshError';
  }
}

export class ValidationError extends Error {
  constructor(message: string, public missingFields?: string[]) {
    super(message);
    this.name = 'ValidationError';
  }
}

// Utility Types
export type NonNullable<T> = T extends null | undefined ? never : T;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
