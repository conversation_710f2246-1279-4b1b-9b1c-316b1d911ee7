/**
 * Request parsing utilities for Trial Balance Lambda
 */

import { APIGatewayProxyEvent } from 'aws-lambda';
import { XeroRequestData, ValidationError } from '../types';

/**
 * Parse and validate request data from API Gateway event
 * 
 * @param event - API Gateway proxy event
 * @returns Validated XeroRequestData
 * @throws ValidationError if request data is invalid
 */
export function parseRequestData(event: APIGatewayProxyEvent): XeroRequestData {
    console.log('📝 Parsing request data from API Gateway event');

    // Parse request body
    let requestBody: any;
    try {
        requestBody = event.body ? JSON.parse(event.body) : {};
    } catch (error) {
        console.error('❌ Failed to parse request body:', error);
        throw new ValidationError('Invalid JSON in request body');
    }

    // Extract companyId from body or path parameters
    const companyId = requestBody.companyId ||
        event.pathParameters?.['companyId'] ||
        event.queryStringParameters?.['companyId'];

    if (!companyId) {
        throw new ValidationError('companyId is required in request body, path parameters, or query parameters');
    }

    // Validate companyId format (should be UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(companyId)) {
        throw new ValidationError('companyId must be a valid UUID');
    }

    // Extract optional parameters
    const tenantId = requestBody.tenantId || event.queryStringParameters?.['tenantId'];
    const startDate = requestBody.startDate || event.queryStringParameters?.['startDate'];
    const endDate = requestBody.endDate || event.queryStringParameters?.['endDate'];

    // Validate date formats if provided
    if (startDate && !isValidDateFormat(startDate)) {
        throw new ValidationError('startDate must be in YYYY-MM-DD format');
    }

    if (endDate && !isValidDateFormat(endDate)) {
        throw new ValidationError('endDate must be in YYYY-MM-DD format');
    }

    // Validate date range if both provided
    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
        throw new ValidationError('startDate must be before or equal to endDate');
    }

    const requestData: XeroRequestData = {
        companyId,
        ...(tenantId && { tenantId }),
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
    };

    console.log('✅ Successfully parsed request data:', {
        companyId: requestData.companyId,
        tenantId: requestData.tenantId ? requestData.tenantId.substring(0, 8) + '...' : undefined,
        startDate: requestData.startDate,
        endDate: requestData.endDate,
    });

    return requestData;
}

/**
 * Validate date format (YYYY-MM-DD)
 * 
 * @param dateString - Date string to validate
 * @returns true if valid, false otherwise
 */
function isValidDateFormat(dateString: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) {
        return false;
    }

    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime()) &&
        date.toISOString().substring(0, 10) === dateString;
}

/**
 * Extract headers for logging purposes
 * 
 * @param event - API Gateway proxy event
 * @returns Relevant headers for logging
 */
export function extractRelevantHeaders(event: APIGatewayProxyEvent): Record<string, string> {
    const relevantHeaders: Record<string, string> = {};

    if (event.headers) {
        // Extract commonly useful headers
        const headersToExtract = [
            'user-agent',
            'x-forwarded-for',
            'x-amzn-requestid',
            'content-type',
            'authorization'
        ];

        headersToExtract.forEach(header => {
            const value = event.headers[header] || event.headers[header.toLowerCase()];
            if (value) {
                // Mask authorization header for security
                relevantHeaders[header] = header === 'authorization' ?
                    value.substring(0, 10) + '...' : value;
            }
        });
    }

    return relevantHeaders;
}
