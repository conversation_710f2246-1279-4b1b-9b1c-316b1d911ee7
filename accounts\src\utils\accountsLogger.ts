/**
 * Accounts Logger Utility
 *
 * This utility provides simplified logging functionality specifically for
 * Accounts Lambda function execution using only the IntegrationLog model.
 * It follows the same pattern as Profit & Loss and Balance Sheet logging.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> Logger Utility
 * @version 1.0.0
 */

import {
  logLambdaInvocation,
  updateLambdaSuccess,
  updateLambdaError,
  formatDuration,
} from './integrationLogger';

/**
 * Accounts Execution Result Interface
 */
export interface AccountsExecutionResult {
  totalAccounts: number;
  processedAccounts: number;
  insertedAccounts: number;
  updatedAccounts: number;
  errors: number;
  warnings: number;
  duration: string;
}

/**
 * Start Accounts Lambda execution logging
 */
export async function startAccountsExecution(
  companyId: string,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ logId: string; startTime: number }> {
  try {
    const startTime = Date.now();
    const logId = await logLambdaInvocation(companyId, 'Accounts', triggeredBy);

    return { logId, startTime };
  } catch (error) {
    console.error('❌ Failed to start Accounts execution logging:', error);
    throw error;
  }
}

/**
 * Log Accounts execution success
 */
export async function logAccountsSuccess(
  logId: string,
  startTime: number,
  result: AccountsExecutionResult
): Promise<void> {
  try {
    const duration = formatDuration(Date.now() - startTime);
    const message = `Accounts sync successful. Processed ${result.processedAccounts} accounts (${result.insertedAccounts} inserted, ${result.updatedAccounts} updated).`;

    const syncSummary = {
      totalAccounts: result.totalAccounts,
      processedAccounts: result.processedAccounts,
      insertedAccounts: result.insertedAccounts,
      updatedAccounts: result.updatedAccounts,
      duration: result.duration,
      errors: result.errors,
      warnings: result.warnings,
    };

    await updateLambdaSuccess(logId, duration, message, syncSummary);
  } catch (logError) {
    console.error('❌ Failed to log Accounts success:', logError);
    // Don't throw here to avoid masking the original success
  }
}

/**
 * Log Accounts execution failure
 */
export async function logAccountsFailure(
  logId: string,
  startTime: number,
  error: Error,
  partialResult?: Partial<AccountsExecutionResult>
): Promise<void> {
  try {
    const duration = formatDuration(Date.now() - startTime);

    const partialSummary = {
      totalAccounts: partialResult?.totalAccounts || 0,
      processedAccounts: partialResult?.processedAccounts || 0,
      insertedAccounts: partialResult?.insertedAccounts || 0,
      updatedAccounts: partialResult?.updatedAccounts || 0,
      duration: partialResult?.duration || duration,
      errors: 1,
      warnings: partialResult?.warnings || 0,
    };

    await updateLambdaError(logId, duration, error, partialSummary);
  } catch (logError) {
    console.error('❌ Failed to log Accounts failure:', logError);
    // Don't throw here to avoid masking the original error
  }
}

/**
 * Wrapper function for complete Accounts execution with logging
 */
export async function executeWithLogging<T>(
  companyId: string,
  operation: () => Promise<T>,
  createResult: (data: T, duration: string) => AccountsExecutionResult,
  triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<T> {
  const startTime = Date.now();
  let logId: string | null = null;

  try {
    // Start execution logging
    const logInfo = await startAccountsExecution(companyId, triggeredBy);
    logId = logInfo.logId;

    // Execute the operation
    const result = await operation();

    // Calculate duration
    const duration = formatDuration(Date.now() - startTime);

    // Create execution result
    const executionResult = createResult(result, duration);

    // Log success
    await logAccountsSuccess(logId, startTime, executionResult);

    return result;
  } catch (error) {
    // Calculate duration
    const duration = formatDuration(Date.now() - startTime);

    // Log failure
    if (logId) {
      await logAccountsFailure(logId, startTime, error as Error, {
        duration,
        errors: 1,
      });
    }

    throw error;
  }
}
