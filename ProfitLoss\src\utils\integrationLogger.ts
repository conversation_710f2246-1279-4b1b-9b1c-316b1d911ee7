/**
 * Integration Logger Utility for Profit & Loss Service
 *
 * This utility provides comprehensive logging functionality for Profit & Loss
 * synchronization operations using the IntegrationLog model.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - API call tracking with detailed metrics
 * - Sync operation monitoring with status management
 * - Error handling and diagnostic information
 * - Performance metrics and timing data
 *
 * <AUTHOR> & Loss Integration Logger
 * @version 1.0.0
 */

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import moment from 'moment';

/**
 * Sync Status Enum (matching Prisma schema)
 */
export enum SyncStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    SUCCESS = 'SUCCESS',
    WARNING = 'WARNING',
    ERROR = 'ERROR',
    RETRYING = 'RETRYING',
    CANCELLED = 'CANCELLED'
}

/**
 * Integration Log Data Interface
 */
export interface IntegrationLogData {
    requestId?: string;
    companyId: string;
    apiName: string;
    method?: string;
    apiUrl?: string;
    integrationName?: string;
    statusCode?: string;
    duration?: string;
    message?: string;
    entity?: string;
    triggeredBy?: 'USER' | 'SYSTEM';
    syncStatus?: SyncStatus;
    apiRequest?: any;
    apiResponse?: any;
    errorDetails?: any;
    startedAt?: Date;
    completedAt?: Date;
}

/**
 * Lambda Execution Context
 */
export interface LambdaExecutionContext {
    requestId: string;
    companyId: string;
    entity: string;
    triggeredBy: 'USER' | 'SYSTEM';
    startTime: number;
}

/**
 * Sync Summary Interface
 */
export interface SyncSummary {
    monthsProcessed: number;
    totalMonths: number;
    trackingRecords: number;
    summaryRecords: number;
    apiCalls: number;
    duration: string;
    errors: number;
    warnings: number;
}

let prisma: PrismaClient | null = null;

/**
 * Get or create Prisma client instance
 */
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

/**
 * Create a new integration log entry
 */
export async function createIntegrationLog(logData: IntegrationLogData): Promise<string> {
    try {
        const integrationLog = await getPrismaClient().integrationLog.create({
            data: {
                RequestId: logData.requestId || uuidv4(),
                CompanyId: logData.companyId,
                ApiName: logData.apiName,
                Method: logData.method || null,
                ApiUrl: logData.apiUrl || null,
                IntegrationName: logData.integrationName || 'Xero',
                StatusCode: logData.statusCode || null,
                Duration: logData.duration || null,
                Message: logData.message || null,
                Entity: logData.entity || null,
                TriggeredBy: logData.triggeredBy || 'SYSTEM',
                SyncStatus: logData.syncStatus || SyncStatus.PENDING,
                ApiRequest: logData.apiRequest || null,
                ApiResponse: logData.apiResponse || null,
                ErrorDetails: logData.errorDetails || null,
                StartedAt: logData.startedAt || new Date(),
                CompletedAt: logData.completedAt || null,
            },
        });

        return integrationLog.Id;
    } catch (error: any) {
        console.error('❌ Failed to create integration log:', {
            error: error.message,
            companyId: logData.companyId,
            apiName: logData.apiName,
        });
        throw error;
    }
}

/**
 * Create Lambda execution context
 */
export function createLambdaContext(
    companyId: string,
    entity: string,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): LambdaExecutionContext {
    return {
        requestId: uuidv4(),
        companyId,
        entity,
        triggeredBy,
        startTime: Date.now(),
    };
}

/**
 * Log Lambda invocation - creates initial log entry that will be updated on completion
 */
export async function logLambdaInvocation(
    companyId: string,
    lambdaName: string,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<string> {
    const requestId = uuidv4();
    const logData: IntegrationLogData = {
        requestId,
        companyId,
        apiName: lambdaName,
        method: 'LAMBDA',
        apiUrl: `/lambda/${lambdaName.toLowerCase()}`,
        integrationName: 'Xero',
        message: `${lambdaName} Lambda invocation started`,
        entity: lambdaName,
        triggeredBy,
        syncStatus: SyncStatus.IN_PROGRESS,
        startedAt: new Date(),
    };

    console.log(`🚀 ${lambdaName} Lambda invocation started for company: ${companyId}`);
    return await createIntegrationLog(logData);
}

/**
 * Update an existing integration log entry
 */
export async function updateIntegrationLog(
    logId: string,
    updateData: Partial<IntegrationLogData>
): Promise<void> {
    try {
        console.log(`📝 Updating integration log ${logId} with status: ${updateData.syncStatus}`);

        await getPrismaClient().integrationLog.update({
            where: { Id: logId },
            data: {
                ...(updateData.statusCode && { StatusCode: updateData.statusCode }),
                ...(updateData.duration && { Duration: updateData.duration }),
                ...(updateData.message && { Message: updateData.message }),
                ...(updateData.syncStatus && { SyncStatus: updateData.syncStatus }),
                ...(updateData.apiRequest && { ApiRequest: updateData.apiRequest }),
                ...(updateData.apiResponse && { ApiResponse: updateData.apiResponse }),
                ...(updateData.errorDetails && { ErrorDetails: updateData.errorDetails }),
                ...(updateData.syncSummary && { SyncSummary: updateData.syncSummary }),
                ...(updateData.completedAt && { CompletedAt: updateData.completedAt }),
                UpdatedAt: new Date(),
            },
        });

        console.log(`✅ Integration log ${logId} updated successfully`);
    } catch (error: any) {
        console.error('❌ Failed to update integration log:', {
            error: error.message,
            logId,
            status: updateData.syncStatus,
        });
        // Don't throw error to avoid breaking the main flow
    }
}

/**
 * Create sync summary
 */
export function createSyncSummary(
    monthsProcessed: number,
    totalMonths: number,
    trackingRecords: number,
    summaryRecords: number,
    apiCalls: number,
    duration: string,
    errors: number,
    warnings: number
): SyncSummary {
    return {
        monthsProcessed,
        totalMonths,
        trackingRecords,
        summaryRecords,
        apiCalls,
        duration,
        errors,
        warnings
    };
}

/**
 * Update Lambda invocation log with success status
 */
export async function updateLambdaSuccess(
    logId: string,
    duration: string,
    message: string,
    syncSummary?: any
): Promise<void> {
    const updateData: Partial<IntegrationLogData> = {
        statusCode: '200',
        duration,
        message,
        syncStatus: SyncStatus.SUCCESS,
        syncSummary,
        completedAt: new Date(),
    };

    await updateIntegrationLog(logId, updateData);
    console.log(`✅ Lambda execution completed successfully: ${message}`);
}

/**
 * Update Lambda invocation log with error status
 */
export async function updateLambdaError(
    logId: string,
    duration: string,
    error: Error,
    partialSummary?: any
): Promise<void> {
    const errorDetails = {
        message: error.message,
        stack: error.stack,
        name: error.name,
    };

    const updateData: Partial<IntegrationLogData> = {
        statusCode: '500',
        duration,
        message: `ProfitLoss (Report) sync failed: ${error.message}`,
        syncStatus: SyncStatus.ERROR,
        errorDetails,
        syncSummary: partialSummary,
        completedAt: new Date(),
    };

    await updateIntegrationLog(logId, updateData);
    console.error(`❌ Lambda execution failed: ${error.message}`);
}
