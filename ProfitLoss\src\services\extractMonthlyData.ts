// Interface for data without tracking categories
export interface ProcessedRowDataWithoutTracking {
    Year: number;
    Month: number;
    AccountId: string;
    AccountName: string;
    Amount: number;
    CompanyId: string;
}

export function extractMonthlyData(report: any, trackingCategories: any, requestData: any) {
    const rows = report.Rows;
    let columnHeaders = [];

    const dateObj = new Date(requestData.endDate);
    const year = dateObj.getFullYear();       // 2025
    const month = dateObj.getMonth() + 1;
    // 1️⃣ find the column headers (first header row)
    for (const row of rows) {
        if (row.RowType === "Header") {
            columnHeaders = row.Cells.map((c: any) => c.Value);
            break;
        }
    }

    // 2️⃣ go through all sections
    const results = [];
    for (const row of rows) {
        if (row.RowType === "Section" && row.Rows) {
            const sectionTitle = row.Title;

            for (const innerRow of row.Rows) {
                if (innerRow.RowType === "Row" && innerRow.Cells) {
                    const firstCell = innerRow.Cells[0];
                    const accountName = firstCell?.Value || null;
                    let accountId = null;

                    if (firstCell?.Attributes) {
                        const accAttr = firstCell.Attributes.find((a: any) => a.Id === "account");
                        accountId = accAttr?.Value || null;
                    }

                    for (let i = 1; i < innerRow.Cells.length; i++) {
                        const cell = innerRow.Cells[i];
                        if (!cell?.Value) continue;

                        const amount = parseFloat(cell.Value.replace(/,/g, "")) || 0;
                        const columnHeader = columnHeaders[i];

                        let trackingCategory1 = null;
                        let trackingCategory2 = null;

                        if (columnHeader && columnHeader.includes(",")) {
                            const [cat1, cat2] = columnHeader.split(",").map((s: any) => s.trim());

                            trackingCategories.forEach((category: any) => {
                                category.options.forEach((option: any) => {
                                    if (option.name === cat1) {
                                        trackingCategory1 = option.id;
                                    }
                                    if (option.name === cat2) {
                                        trackingCategory2 = option.id;
                                    }
                                });
                            });
                        } else {
                            trackingCategories.forEach((category: any) => {
                                category.options.forEach((option: any) => {
                                    if (option.name === columnHeader) {
                                        trackingCategory1 = option.id;
                                    }
                                });
                            });
                        }

                        if (accountId) {
                            results.push({
                                Year: year,
                                Month: month,
                                AccountId: accountId,
                                AccountName: accountName,
                                Amount: amount,
                                TrackingCategoryId1: trackingCategory1,
                                TrackingCategoryId2: trackingCategory2,
                                CompanyId: requestData.companyId,
                            });
                        }
                    }
                }
            }
        }
    }


    return results;
}

// New function to extract data without tracking categories (aggregated by account)
export function extractMonthlyDataWithoutTracking(report: any, requestData: any): ProcessedRowDataWithoutTracking[] {
    const rows = report.Rows;
    const dateObj = new Date(requestData.endDate);
    const year = dateObj.getFullYear();
    const month = dateObj.getMonth() + 1;

    // Map to aggregate amounts by account
    const accountMap = new Map<string, { accountName: string; totalAmount: number }>();

    // Go through all sections
    for (const row of rows) {
        if (row.RowType === "Section" && row.Rows) {
            for (const innerRow of row.Rows) {
                if (innerRow.RowType === "Row" && innerRow.Cells) {
                    const firstCell = innerRow.Cells[0];
                    const accountName = firstCell?.Value || null;
                    let accountId = null;

                    if (firstCell?.Attributes) {
                        const accAttr = firstCell.Attributes.find((a: any) => a.Id === "account");
                        accountId = accAttr?.Value || null;
                    }

                    if (!accountId || !accountName) continue;

                    // Sum all amounts for this account (excluding the first cell which is the account name)
                    let totalAmount = 0;
                    for (let i = 1; i < innerRow.Cells.length; i++) {
                        const cell = innerRow.Cells[i];
                        if (cell?.Value) {
                            const amount = parseFloat(cell.Value.replace(/,/g, "")) || 0;
                            totalAmount += amount;
                        }
                    }

                    // Aggregate amounts by account
                    if (accountMap.has(accountId)) {
                        const existing = accountMap.get(accountId)!;
                        existing.totalAmount += totalAmount;
                    } else {
                        accountMap.set(accountId, {
                            accountName,
                            totalAmount
                        });
                    }
                }
            }
        }
    }

    // Convert map to results array
    const results: ProcessedRowDataWithoutTracking[] = [];
    for (const [accountId, data] of accountMap.entries()) {
        if (data.totalAmount !== 0) { // Only include accounts with non-zero amounts
            results.push({
                Year: year,
                Month: month,
                AccountId: accountId,
                AccountName: data.accountName,
                Amount: data.totalAmount,
                CompanyId: requestData.companyId,
            });
        }
    }

    return results;
}
