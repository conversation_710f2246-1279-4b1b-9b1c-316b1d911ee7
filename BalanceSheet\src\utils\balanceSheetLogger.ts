/**
 * Balance Sheet Logger Utility
 *
 * This utility provides simplified logging functionality specifically for
 * Balance Sheet Lambda function execution using only the IntegrationLog model.
 * It bypasses the missing ApiLog and SyncLog models.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> Sheet Logger Utility
 * @version 1.0.0
 */

import {
    createLambdaContext,
    logLambdaStart,
    logLambdaSuccess,
    logLambdaFailure,
    createSyncSummary,
    LambdaExecutionContext,
    SyncSummary
} from './integrationLogger';

// Re-export types for external use
export type { LambdaExecutionContext, SyncSummary };

/**
 * Balance Sheet execution result interface
 */
export interface BalanceSheetExecutionResult {
    success: boolean;
    monthsProcessed: number;
    totalMonths: number;
    trackingRecords?: number;
    summaryRecords?: number;
    apiCalls?: number;
    errors: number;
    warnings: number;
    duration: string;
    errorMessage?: string;
    errorDetails?: any;
}

/**
 * Start Balance Sheet Lambda execution logging
 */
export async function startBalanceSheetExecution(
    companyId: string,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ context: LambdaExecutionContext; logId: string }> {
    console.log(`🚀 Starting Balance Sheet execution logging for company: ${companyId}`);

    try {
        // Create Lambda execution context
        const context = createLambdaContext(companyId, 'BalanceSheet', triggeredBy);

        // Log Lambda start
        const logId = await logLambdaStart(context);

        console.log(`📝 Balance Sheet execution started - Log ID: ${logId}, Request ID: ${context.requestId}`);

        return { context, logId };
    } catch (error) {
        console.error('❌ Failed to start Balance Sheet execution logging:', error);
        throw error;
    }
}

/**
 * Log successful Balance Sheet execution
 */
export async function logBalanceSheetSuccess(
    logId: string,
    context: LambdaExecutionContext,
    result: BalanceSheetExecutionResult
): Promise<void> {
    console.log(`✅ Logging Balance Sheet success for Log ID: ${logId}`);

    try {
        // Create sync summary
        const syncSummary = createSyncSummary(
            result.totalMonths,
            result.monthsProcessed,
            result.trackingRecords || 0,
            result.summaryRecords || 0,
            result.apiCalls || result.monthsProcessed * 2, // Default: 2 API calls per month
            result.errors,
            result.warnings,
            result.duration
        );

        // Log Lambda success
        await logLambdaSuccess(logId, context, syncSummary);

        console.log(`📊 Balance Sheet execution completed successfully:`);
        console.log(`   📈 Processed: ${result.monthsProcessed}/${result.totalMonths} months`);
        console.log(`   📊 Records: ${(result.trackingRecords || 0) + (result.summaryRecords || 0)} total`);
        console.log(`   🔄 API Calls: ${syncSummary.apiCalls}`);
        console.log(`   ⏱️ Duration: ${result.duration}`);

    } catch (error) {
        console.error('❌ Failed to log Balance Sheet success:', error);
        throw error;
    }
}

/**
 * Log failed Balance Sheet execution
 */
export async function logBalanceSheetFailure(
    logId: string,
    context: LambdaExecutionContext,
    error: Error,
    partialResult?: Partial<BalanceSheetExecutionResult>
): Promise<void> {
    console.log(`❌ Logging Balance Sheet failure for Log ID: ${logId}`);

    try {
        // Create partial sync summary
        const partialSummary = {
            totalMonths: partialResult?.totalMonths || 0,
            processedMonths: partialResult?.monthsProcessed || 0,
            trackingRecords: partialResult?.trackingRecords || 0,
            summaryRecords: partialResult?.summaryRecords || 0,
            apiCalls: partialResult?.apiCalls || 0,
            errors: 1,
            warnings: partialResult?.warnings || 0,
            duration: partialResult?.duration || '0s'
        };

        // Log Lambda failure
        await logLambdaFailure(logId, context, error, partialSummary);

        console.log(`💥 Balance Sheet execution failed:`);
        console.log(`   📈 Processed: ${partialSummary.processedMonths}/${partialSummary.totalMonths} months`);
        console.log(`   ❌ Error: ${error.message}`);
        console.log(`   ⏱️ Duration: ${partialSummary.duration}`);

    } catch (logError) {
        console.error('❌ Failed to log Balance Sheet failure:', logError);
        // Don't throw here to avoid masking the original error
    }
}

/**
 * Wrapper function for complete Balance Sheet execution with logging
 */
export async function executeWithLogging<T>(
    companyId: string,
    operation: () => Promise<T>,
    createResult: (data: T, duration: string) => BalanceSheetExecutionResult,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<T> {
    const startTime = Date.now();
    let logId: string | null = null;
    let context: LambdaExecutionContext | null = null;

    try {
        // Start execution logging
        const logInfo = await startBalanceSheetExecution(companyId, triggeredBy);
        logId = logInfo.logId;
        context = logInfo.context;

        // Execute the operation
        const result = await operation();

        // Calculate duration
        const duration = formatDuration(Date.now() - startTime);

        // Create execution result
        const executionResult = createResult(result, duration);

        // Log success
        await logBalanceSheetSuccess(logId, context, executionResult);

        return result;

    } catch (error) {
        // Calculate duration
        const duration = formatDuration(Date.now() - startTime);

        // Log failure
        if (logId && context) {
            await logBalanceSheetFailure(logId, context, error as Error, {
                duration,
                errors: 1
            });
        }

        throw error;
    }
}

/**
 * Format duration in human-readable format
 */
function formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);

    if (minutes > 0) {
        const remainingSeconds = seconds % 60;
        return `${minutes}m ${remainingSeconds}s`;
    } else {
        return `${seconds}s`;
    }
}

/**
 * Create a simple execution result for basic operations
 */
export function createSimpleExecutionResult(
    monthsProcessed: number,
    totalMonths: number,
    duration: string,
    errors: number = 0,
    warnings: number = 0
): BalanceSheetExecutionResult {
    return {
        success: errors === 0,
        monthsProcessed,
        totalMonths,
        errors,
        warnings,
        duration
    };
}

/**
 * Create a detailed execution result with record counts
 */
export function createDetailedExecutionResult(
    monthsProcessed: number,
    totalMonths: number,
    trackingRecords: number,
    summaryRecords: number,
    apiCalls: number,
    duration: string,
    errors: number = 0,
    warnings: number = 0
): BalanceSheetExecutionResult {
    return {
        success: errors === 0,
        monthsProcessed,
        totalMonths,
        trackingRecords,
        summaryRecords,
        apiCalls,
        errors,
        warnings,
        duration
    };
}
