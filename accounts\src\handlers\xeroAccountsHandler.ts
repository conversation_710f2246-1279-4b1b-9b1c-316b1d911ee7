/**
 * Xero Accounts Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Chart of Accounts data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of accounts
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /accounts/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company, Account } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';

import dotenv from 'dotenv';
dotenv.config();

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        // SQS
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context);
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err; // let Lambda/SQS retry
            }
        }
        return;
    } else {
        // API Gateway
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context);

            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "Chart of Accounts data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }

    // handle API Gateway
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }

    throw new ValidationError("Invalid request data");
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context
): Promise<void> {
    try {
        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error("Active integration not found");
        }

        const validIntegration = await ensureValidToken(integration);

        console.log(`🚀 Fetching Chart of Accounts for company: ${requestData.companyId}`);

        // Fetch accounts from Xero
        const accountsData = await getAccounts(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (accountsData && accountsData.length > 0) {
            if (requestData.dumpToDatabase) {
                await saveAccountsToDatabase(accountsData, requestData.companyId, prisma);
                console.log(`✅ Successfully processed ${accountsData.length} accounts`);
            } else {
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No accounts found to process");
        }

    } catch (error) {
        console.error("Error processing accounts request:", error);
        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });

    if (!integration) {
        throw new Error("Active integration not found for company");
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add a 10 minute buffer (600,000 ms)
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get accounts from Xero
const getAccounts = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}/Accounts`;

        console.log(`Fetching accounts from Xero API: ${url}`);

        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                'Accept': 'application/json'
            },
            params: requestData.where ? { where: requestData.where } : {},
        });

        // Log the full Xero response for debugging
        console.log('Xero Accounts API raw response:', JSON.stringify(response.data, null, 2));

        if (response.data && response.data.Accounts) {
            console.log(`Retrieved ${response.data.Accounts.length} accounts from Xero`);
            return response.data.Accounts;
        }

        return [];
    } catch (error: any) {
        console.error("Error fetching accounts from Xero:", error.response?.data || error.message);
        throw new XeroError(`Failed to fetch accounts: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
    }
};

// Helper function to parse Xero's .NET date format
function parseXeroDate(dateString: string | null | undefined): Date | null {
    if (!dateString || typeof dateString !== 'string') return null;

    // Handle Xero's .NET date format: "/Date(*************+0000)/"
    const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
    if (match) {
        const timestamp = parseInt(match[1] || "0");
        return new Date(timestamp);
    }

    // Try parsing as regular date string
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

// Save accounts to database
async function saveAccountsToDatabase(
    accounts: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<void> {
    console.log(`Saving ${accounts.length} accounts to database`);

    // 1. Map all accounts to DB shape
    const accountRecords = accounts.map(account => ({
        AccountID: account.AccountID,
        BankAccountType: account.BankAccountType || null,
        BankAccountNumber: account.BankAccountNumber || null,
        Code: account.Code || null,
        AccountClassTypes: account.Class || null,
        Type: account.Type || null,
        Name: account.Name || null,
        Description: account.Description || null,
        ReportingCode: account.ReportingCode || null,
        ReportingCodeName: account.ReportingCodeName || null,
        CurrencyCode: account.CurrencyCode || null,
        TaxType: account.TaxType || null,
        SystemAccount: account.SystemAccount || null,
        Status: account.Status || null,
        EnablePaymentsToAccount: account.EnablePaymentsToAccount || false,
        ShowInExpenseClaims: account.ShowInExpenseClaims || false,
        UpdateUtcDate: parseXeroDate(account.UpdatedDateUTC) || new Date(),
        CompanyId: companyId
    }));
    const accountIds = accountRecords.map(a => a.AccountID);

    // 2. Fetch existing AccountIDs
    const existingAccounts = await prisma.account.findMany({
        where: { AccountID: { in: accountIds } },
        select: { AccountID: true }
    });
    const existingAccountIds = new Set(existingAccounts.map(a => a.AccountID));

    // 3. Split into new and existing
    const accountsToInsert = accountRecords.filter(a => !existingAccountIds.has(a.AccountID));
    const accountsToUpdate = accountRecords.filter(a => existingAccountIds.has(a.AccountID));

    // 4. Bulk insert new accounts
    if (accountsToInsert.length > 0) {
        await prisma.account.createMany({ data: accountsToInsert, skipDuplicates: true });
        console.log(`Inserted ${accountsToInsert.length} new accounts`);
    }
    // 5. Bulk update existing accounts
    if (accountsToUpdate.length > 0) {
        await prisma.$transaction(
            accountsToUpdate.map(account =>
                prisma.account.update({
                    where: { AccountID: account.AccountID },
                    data: account
                })
            )
        );
        console.log(`Updated ${accountsToUpdate.length} existing accounts`);
    }
}
