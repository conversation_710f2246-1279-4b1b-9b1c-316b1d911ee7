/**
 * Xero Accounts Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Chart of Accounts data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of accounts
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /accounts/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig, PRODUCTION_CONFIG } from '../config/environment';
import { XeroRequestData, ValidationError, XeroError } from '../types';
import {
    startAccountsExecution,
    logAccountsSuccess,
    logAccountsFailure,
    AccountsExecutionResult,
} from '../utils/accountsLogger';

import dotenv from 'dotenv';
dotenv.config();

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ('Records' in event && event.Records?.length) {
        // Handle SQS event - batch processing
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context, 'SYSTEM');
            } catch (err) {
                console.error('❌ Failed to process SQS message', err);
                // Re-throw error to trigger SQS retry mechanism
                throw err;
            }
        }
        return;
    } else {
        // Handle API Gateway event - direct HTTP request
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context, 'USER');

            return {
                statusCode: 200,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: true,
                    message: 'Chart of Accounts data processed successfully',
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error('❌ Handler error:', err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ('Records' in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse SQS message body');
        }
    }

    // handle API Gateway
    if ('body' in event && event.body) {
        try {
            const parsed = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse API Gateway body');
        }
    }

    throw new ValidationError('Invalid request data');
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<void> {
    const startTime = Date.now();
    let integrationLogId: string | null = null;
    let accountsProcessed = 0;
    let accountsInserted = 0;
    let accountsUpdated = 0;

    try {
        // Start execution logging
        const { logId } = await startAccountsExecution(requestData.companyId, triggeredBy);
        integrationLogId = logId;

        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error('Active integration not found');
        }

        const validIntegration = await ensureValidToken(integration);

        console.log(`🚀 Fetching Chart of Accounts for company: ${requestData.companyId}`);

        // Fetch accounts from Xero
        const accountsData = await getAccounts(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (accountsData && accountsData.length > 0) {
            if (requestData.dumpToDatabase) {
                const saveResult = await saveAccountsToDatabase(
                    accountsData,
                    requestData.companyId,
                    prisma
                );
                accountsProcessed = accountsData.length;
                accountsInserted = saveResult.inserted;
                accountsUpdated = saveResult.updated;

                console.log(
                    `✅ Successfully processed ${accountsProcessed} accounts (${accountsInserted} inserted, ${accountsUpdated} updated)`
                );
            } else {
                console.log('📊 Data fetched but not dumped to DB (dumpToDatabase is false)');
                accountsProcessed = accountsData.length;
            }
        } else {
            console.log('📭 No accounts found to process');
        }

        // Log success
        if (integrationLogId) {
            const executionResult: AccountsExecutionResult = {
                totalAccounts: accountsData?.length || 0,
                processedAccounts: accountsProcessed,
                insertedAccounts: accountsInserted,
                updatedAccounts: accountsUpdated,
                errors: 0,
                warnings: 0,
                duration: `${Date.now() - startTime}ms`,
            };

            await logAccountsSuccess(integrationLogId, startTime, executionResult);
        }

        const totalDuration = Date.now() - startTime;
        console.log(`✅ Accounts sync completed successfully in ${(totalDuration / 1000).toFixed(1)}s`);
    } catch (error: any) {
        // Log failure
        if (integrationLogId) {
            await logAccountsFailure(integrationLogId, startTime, error, {
                totalAccounts: 0,
                processedAccounts: accountsProcessed,
                insertedAccounts: accountsInserted,
                updatedAccounts: accountsUpdated,
                errors: 1,
                warnings: 0,
            });
        }

        const totalDuration = Date.now() - startTime;
        console.error(`❌ Accounts sync failed after ${(totalDuration / 1000).toFixed(1)}s:`, {
            error: error.message,
            companyId: requestData.companyId,
        });
        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError('companyId is required');
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: 'ACTIVE',
        },
    });

    if (!integration) {
        throw new Error('Active integration not found for company');
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error('No access token found');
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add configurable buffer before token expiry
    if (expiry.getTime() - now.getTime() <= PRODUCTION_CONFIG.TOKEN_REFRESH_BUFFER_MS) {
        console.log('Token expired or about to expire, refreshing...');
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get accounts from Xero with improved error handling and timeout configuration
const getAccounts = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    const startTime = Date.now();

    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}Accounts`;

        console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`);

        const response = await axios.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
                'User-Agent': 'AccountsSync/1.0.0',
            },
            params: requestData.where ? { where: requestData.where } : {},
            timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS, // Configurable API timeout
        });

        const requestTime = Date.now() - startTime;
        console.log(`✅ Accounts API call completed in ${requestTime}ms`);

        const accountsData = response?.data;
        if (!accountsData || !accountsData.Accounts) {
            throw new Error('Invalid Accounts data structure received from Xero');
        }


        console.log(`📊 Retrieved ${accountsData.Accounts.length} accounts from Xero`);
        return accountsData.Accounts;
    } catch (error: any) {
        const requestTime = Date.now() - startTime;
        console.error(`❌ Accounts API call failed after ${requestTime}ms:`, {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            tenantId: tenantId.substring(0, 8) + '...',
        });

        // Handle specific Xero API errors
        if (error.response?.status === 429) {
            throw new XeroError(`Rate limit exceeded. Please try again later.`, 429, error);
        }

        if (error.response?.status === 401) {
            throw new XeroError(`Authentication failed. Token may be expired.`, 401, error);
        }

        if (error.response?.status === 403) {
            throw new XeroError(`Access forbidden. Check tenant permissions.`, 403, error);
        }

        // Extract Xero-specific error message if available
        const xeroErrorMessage =
            error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message ||
            error.response?.data?.message ||
            error.message;

        throw new XeroError(
            `Failed to fetch accounts: ${xeroErrorMessage}`,
            error.response?.status,
            error
        );
    }
};

// Helper function to parse Xero's .NET date format
function parseXeroDate(dateString: string | null | undefined): Date | null {
    if (!dateString || typeof dateString !== 'string') return null;

    // Handle Xero's .NET date format: "/Date(*************+0000)/"
    const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
    if (match) {
        const timestamp = parseInt(match[1] || '0');
        return new Date(timestamp);
    }

    // Try parsing as regular date string
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

// Save accounts to database
async function saveAccountsToDatabase(
    accounts: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<{ inserted: number; updated: number }> {
    console.log(`Saving ${accounts.length} accounts to database`);

    // 1. Map all accounts to DB shape
    const accountRecords = accounts.map(account => ({
        AccountID: account.AccountID,
        BankAccountType: account.BankAccountType || null,
        BankAccountNumber: account.BankAccountNumber || null,
        Code: account.Code || null,
        AccountClassTypes: account.Class || null,
        Type: account.Type || null,
        Name: account.Name || null,
        Description: account.Description || null,
        ReportingCode: account.ReportingCode || null,
        ReportingCodeName: account.ReportingCodeName || null,
        CurrencyCode: account.CurrencyCode || null,
        TaxType: account.TaxType || null,
        SystemAccount: account.SystemAccount || null,
        Status: account.Status || null,
        EnablePaymentsToAccount: account.EnablePaymentsToAccount || false,
        ShowInExpenseClaims: account.ShowInExpenseClaims || false,
        UpdateUtcDate: parseXeroDate(account.UpdatedDateUTC) || new Date(),
        CompanyId: companyId,
    }));
    const accountIds = accountRecords.map(a => a.AccountID);

    // 2. Fetch existing AccountIDs
    const existingAccounts = await prisma.account.findMany({
        where: { AccountID: { in: accountIds } },
        select: { AccountID: true },
    });
    const existingAccountIds = new Set(existingAccounts.map(a => a.AccountID));

    // 3. Split into new and existing
    const accountsToInsert = accountRecords.filter(a => !existingAccountIds.has(a.AccountID));
    const accountsToUpdate = accountRecords.filter(a => existingAccountIds.has(a.AccountID));

    // 4. Bulk insert new accounts
    if (accountsToInsert.length > 0) {
        await prisma.account.createMany({ data: accountsToInsert, skipDuplicates: true });
        console.log(`📥 Inserted ${accountsToInsert.length} new accounts`);
    }

    // 5. Bulk update existing accounts
    if (accountsToUpdate.length > 0) {
        await prisma.$transaction(
            accountsToUpdate.map(account =>
                prisma.account.update({
                    where: { AccountID: account.AccountID },
                    data: account,
                })
            )
        );
        console.log(`🔄 Updated ${accountsToUpdate.length} existing accounts`);
    }

    return {
        inserted: accountsToInsert.length,
        updated: accountsToUpdate.length,
    };
}
