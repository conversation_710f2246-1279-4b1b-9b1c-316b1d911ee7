/**
 * Xero Organisations Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Organisation data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of organisations
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /organisations/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company, Account } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';

import dotenv from 'dotenv';
dotenv.config();

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        // SQS
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context);
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err; // let Lambda/SQS retry
            }
        }
        return;
    } else {
        // API Gateway
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context);

            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "Organisation data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }

    // handle API Gateway
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }

    throw new ValidationError("Invalid request data");
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context
): Promise<void> {
    try {
        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error("Active integration not found");
        }

        const validIntegration = await ensureValidToken(integration);

        console.log(`🚀 Fetching Organisations for company: ${requestData.companyId}`);

        // Fetch organisations from Xero
        const organisationsData = await getOrganisations(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (organisationsData && organisationsData.length > 0) {
            if (requestData.dumpToDatabase) {
                await saveOrganisationsToDatabase(organisationsData, requestData.companyId, prisma);
                console.log(`✅ Successfully processed ${organisationsData.length} organisations`);
            } else {
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No organisations found to process");
        }

    } catch (error) {
        console.error("Error processing organisations request:", error);
        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });

    if (!integration) {
        throw new Error("Active integration not found for company");
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add a 10 minute buffer (600,000 ms)
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get organisations from Xero
const getOrganisations = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}/Organisation`;

        console.log(`Fetching organisations from Xero API: ${url}`);

        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                'Accept': 'application/json'
            },
            params: requestData.where ? { where: requestData.where } : {},
        });

        // Log the full Xero response for debugging
        console.log('Xero Organisations API raw response:', JSON.stringify(response.data, null, 2));

        if (response.data && response.data.Organisations) {
            console.log(`Retrieved ${response.data.Organisations.length} organisations from Xero`);
            return response.data.Organisations;
        }

        return [];
    } catch (error: any) {
        console.error("Error fetching organisations from Xero:", error.response?.data || error.message);
        throw new XeroError(`Failed to fetch organisations: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
    }
};

// Helper function to parse Xero's .NET date format
function parseXeroDate(dateString: string | null | undefined): Date | null {
    if (!dateString || typeof dateString !== 'string') return null;

    // Handle Xero's .NET date format: "/Date(1751362260967+0000)/"
    const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
    if (match) {
        const timestamp = parseInt(match[1] || "0");
        return new Date(timestamp);
    }

    // Try parsing as regular date string
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

// Helper: Extract Mailing and Street Address
function extractAddresses(addresses: any[] = []) {
    let mailing: any = {};
    let street: any = {};
    addresses.forEach(addr => {
        // AddressType: 0 = Mailing, 1 = Street
        if (addr.AddressType === 'POBOX' || addr.AddressType === 0) {
            mailing = addr;
        } else {
            street = addr;
        }
    });
    return { mailing, street };
}

// Helper: Extract Phones by Type
function extractPhones(phones: any[] = []) {
    const result: Record<string, any> = {};
    phones.forEach(phone => {
        switch (phone.PhoneType) {
            case 'DEFAULT':
                result.default = phone;
                break;
            case 'DDI':
                result.ddi = phone;
                break;
            case 'MOBILE':
                result.mobile = phone;
                break;
            case 'FAX':
                result.fax = phone;
                break;
            case 'OFFICE':
                result.office = phone;
                break;
        }
    });
    return result;
}

// Map a single Xero organisation to Prisma Organisation shape
function mapXeroOrganisationToPrisma(org: any, companyId: string) {
    // 1. Extract Mailing and Street addresses from Xero response
    const { mailing, street } = extractAddresses(org.Addresses);
    // 2. Extract all phone types from Xero response
    const phones = extractPhones(org.Phones);
    // 3. Map all fields from Xero to Prisma Organisation model
    return {
        // --- Direct field mappings ---
        OrganisationID: org.OrganisationID, // Xero unique ID
        APIKey: org.APIKey, // Xero API Key
        Name: org.Name, // Organisation name
        LegalName: org.LegalName, // Legal name
        ShortCode: org.ShortCode, // Short code
        OrganisationType: org.OrganisationType, // Type
        CountryCode: org.CountryCode, // Country
        BaseCurrency: org.BaseCurrency, // Currency
        IsDemoCompany: org.IsDemoCompany, // Demo flag
        FinancialYearEndDay: org.FinancialYearEndDay, // FY end day
        FinancialYearEndMonth: org.FinancialYearEndMonth, // FY end month
        PaysTax: org.PaysTax, // Pays tax
        TaxNumber: org.TaxNumber, // Tax number
        Timezone: org.Timezone, // Timezone
        Version: org.Version, // Version
        // --- Date fields (parsed) ---
        EndOfYearLockDate: parseXeroDate(org.EndOfYearLockDate),
        PeriodLockDate: parseXeroDate(org.PeriodLockDate),
        UpdateUTCDate: parseXeroDate(org.CreatedDateUTC),
        // --- Mailing Address fields ---
        MailingAddressAttensionTo: mailing.AttentionTo || null,
        MailingAddressAddressLine1: mailing.AddressLine1 || null,
        MailingAddressAddressLine2: mailing.AddressLine2 || null,
        MailingAddressAddressLine3: mailing.AddressLine3 || null,
        MailingAddressAddressLine4: mailing.AddressLine4 || null,
        MailingAddressCity: mailing.City || null,
        MailingAddressCountry: mailing.Country || null,
        MailingAddressPostalCode: mailing.PostalCode || null,
        MailingAddressRegion: mailing.Region || null,
        // --- Street Address fields ---
        StreetAttensionTo: street.AttentionTo || null,
        StreetAddressLine1: street.AddressLine1 || null,
        StreetAddressLine2: street.AddressLine2 || null,
        StreetAddressLine3: street.AddressLine3 || null,
        StreetAddressLine4: street.AddressLine4 || null,
        StreetCity: street.City || null,
        StreetCountry: street.Country || null,
        StreetPostalCode: street.PostalCode || null,
        StreetRegion: street.Region || null,
        // --- Phone fields by type ---
        PhoneDefaultNumber: phones.default?.PhoneNumber || null,
        PhoneDefaultAreaCode: phones.default?.PhoneAreaCode || null,
        PhoneDefaultCountryCode: phones.default?.PhoneCountryCode || null,
        PhoneDDINumber: phones.ddi?.PhoneNumber || null,
        PhoneDDIAreaCode: phones.ddi?.PhoneAreaCode || null,
        PhoneDDICountryCode: phones.ddi?.PhoneCountryCode || null,
        PhoneMobileNumber: phones.mobile?.PhoneNumber || null,
        PhoneMobileAreaCode: phones.mobile?.PhoneAreaCode || null,
        PhoneMobileCountryCode: phones.mobile?.PhoneCountryCode || null,
        PhoneFaxNumber: phones.fax?.PhoneNumber || null,
        PhoneFaxAreaCode: phones.fax?.PhoneAreaCode || null,
        PhoneFaxCountryCode: phones.fax?.PhoneCountryCode || null,
        PhoneOfficeNumber: phones.office?.PhoneNumber || null,
        PhoneOfficeAreaCode: phones.office?.PhoneAreaCode || null,
        PhoneOfficeCountryCode: phones.office?.PhoneCountryCode || null,
        // --- Foreign key ---
        CompanyId: companyId // Link to Company
    };
}

// Save organisations to database
async function saveOrganisationsToDatabase(
    organisations: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<void> {
    // Step 1: Map all Xero organisations to Prisma Organisation records
    console.log(`Saving ${organisations.length} organisations to database`);
    const organisationRecords = organisations.map(org => mapXeroOrganisationToPrisma(org, companyId));

    // Step 2: Find all existing OrganisationIDs for this company in the database
    const existingOrganisations = await prisma.organisation.findMany({
        where: { CompanyId: companyId },
        select: { OrganisationID: true }
    });
    const existingOrganisationIds = new Set(existingOrganisations.map(o => o.OrganisationID));

    // Step 3: Split into new and existing organisations
    const organisationsToInsert = organisationRecords.filter(o => !existingOrganisationIds.has(o.OrganisationID));
    const organisationsToUpdate = organisationRecords.filter(o => existingOrganisationIds.has(o.OrganisationID));

    // Step 4: Bulk insert new organisations
    if (organisationsToInsert.length > 0) {
        await prisma.organisation.createMany({ data: organisationsToInsert, skipDuplicates: true });
        console.log(`Inserted ${organisationsToInsert.length} new organisations`);
    }

    // Step 5: Bulk update existing organisations
    if (organisationsToUpdate.length > 0) {
        await Promise.all(
            organisationsToUpdate.map(org =>
                prisma.organisation.update({
                    where: { OrganisationID: org.OrganisationID },
                    data: org
                })
            )
        );
        console.log(`Updated ${organisationsToUpdate.length} existing organisations`);
    }
}
