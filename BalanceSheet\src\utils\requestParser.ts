import {
    APIGatewayProxyEvent,
    SQSEvent,
} from 'aws-lambda';
import { XeroRequestData, ValidationError } from '../types';

export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ('Records' in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse SQS message body');
        }
    }

    // handle API Gateway
    if ('body' in event && event.body) {
        try {
            const parsed =
                typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError('Failed to parse API Gateway body');
        }
    }

    throw new ValidationError('Invalid request data');
}
  