/**
 * Environment Configuration for Trial Balance Lambda
 */

import { ProductionConfig } from '../types';

/**
 * Get Xero API configuration from environment variables
 */
export function getXeroConfig() {
    const baseUrl = process.env['XERO_BASE_URL'] || 'https://api.xero.com/api.xro/2.0/';
    const clientId = process.env['XERO_CLIENT_ID'];
    const clientSecret = process.env['XERO_CLIENT_SECRET'];
    const tokenUrl = process.env['XERO_TOKEN_URL'] || 'https://identity.xero.com/connect/token';
    const redirectUri = process.env['XERO_REDIRECT_URI'];

    if (!clientId || !clientSecret || !redirectUri) {
        throw new Error('Missing required Xero configuration environment variables');
    }

    return {
        baseUrl,
        clientId,
        clientSecret,
        redirectUri,
        tokenUrl
    };
}

/**
 * Get database configuration
 */
export function getDatabaseConfig() {
    const databaseUrl = process.env['DATABASE_URL'];

    if (!databaseUrl) {
        throw new Error('DATABASE_URL environment variable is required');
    }

    return {
        databaseUrl,
    };
}

/**
 * Production configuration for API calls and processing
 * Enhanced based on Balance Sheet optimizations and Xero API rate limiting best practices
 */
export const PRODUCTION_CONFIG: ProductionConfig = {
    API_TIMEOUT_MS: 45000, // Increased from 30s to 45s for slower responses
    MAX_CONCURRENT_REQUESTS: 1, // Reduced to 1 for strict rate limiting
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY_MS: 2000, // Base delay for exponential backoff
    // New rate limiting configurations
    XERO_API_DELAY_MS: 2000, // Minimum delay between API calls (2 seconds)
    MAX_RETRY_DELAY_MS: 60000, // Maximum retry delay (60 seconds)
    EXPONENTIAL_BACKOFF_MULTIPLIER: 2, // Multiplier for exponential backoff
};

/**
 * Get current stage/environment
 */
export function getStage(): string {
    return process.env['STAGE'] || 'dev';
}

/**
 * Check if running in production
 */
export function isProduction(): boolean {
    return getStage() === 'prod';
}

/**
 * Get AWS region
 */
export function getAwsRegion(): string {
    return process.env['AWS_REGION'] || 'us-east-1';
}
