/**
 * Profit & Loss Logger Utility
 *
 * This utility provides simplified logging functionality specifically for
 * Profit & Loss Lambda function execution using only the IntegrationLog model.
 * It follows the same pattern as Balance Sheet logging.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> & Loss Logger Utility
 * @version 1.0.0
 */

import {
    logLambdaInvocation,
    updateLambdaSuccess,
    updateLambdaError,
    formatDuration
} from './integrationLogger';

/**
 * Profit & Loss Execution Result Interface
 */
export interface ProfitLossExecutionResult {
    monthsProcessed: number;
    totalMonths: number;
    trackingRecords: number;
    summaryRecords: number;
    apiCalls: number;
    duration: string;
    errors: number;
    warnings: number;
}

/**
 * Format duration from milliseconds to human readable string
 */
export function formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    } else {
        return `${seconds}s`;
    }
}

/**
 * Start Profit & Loss Lambda execution logging
 */
export async function startProfitLossExecution(
    companyId: string,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ logId: string; startTime: number }> {
    try {
        const startTime = Date.now();
        const logId = await logLambdaInvocation(companyId, 'ProfitLoss', triggeredBy);

        return { logId, startTime };
    } catch (error) {
        console.error('❌ Failed to start Profit & Loss execution logging:', error);
        throw error;
    }
}

/**
 * Log Profit & Loss execution success
 */
export async function logProfitLossSuccess(
    logId: string,
    startTime: number,
    monthsProcessed: number,
    totalRecords: number = 0
): Promise<void> {
    try {
        const duration = formatDuration(Date.now() - startTime);
        const message = `ProfitLoss(Report) sync successful. Processed ${monthsProcessed} months with ${totalRecords} total records.`;

        const syncSummary = {
            monthsProcessed,
            totalRecords,
            duration,
            errors: 0,
            warnings: 0
        };

        await updateLambdaSuccess(logId, duration, message, syncSummary);

    } catch (logError) {
        console.error('❌ Failed to log Profit & Loss success:', logError);
        // Don't throw here to avoid masking the original success
    }
}

/**
 * Log Profit & Loss execution failure
 */
export async function logProfitLossFailure(
    logId: string,
    startTime: number,
    error: Error,
    monthsProcessed: number = 0,
    totalRecords: number = 0
): Promise<void> {
    try {
        const duration = formatDuration(Date.now() - startTime);

        const partialSummary = {
            monthsProcessed,
            totalRecords,
            duration,
            errors: 1,
            warnings: 0
        };

        await updateLambdaError(logId, duration, error, partialSummary);

    } catch (logError) {
        console.error('❌ Failed to log Profit & Loss failure:', logError);
        // Don't throw here to avoid masking the original error
    }
}

// Simplified Profit & Loss logging - only Lambda invocation level logging
