/**
 * Xero Credit Notes Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Credit Notes and their line items from Xero API.
 *
 * Key Features:
 * - Bulk insert, update, and delete of credit notes and their lines
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /creditnotes/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company } from '@prisma/client';
import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';
import dotenv from 'dotenv';
dotenv.config();

let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context);
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err;
            }
        }
        return;
    } else {
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context);
            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "CreditNotes data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }
    throw new ValidationError("Invalid request data");
}

async function processRequest(
    requestData: XeroRequestData,
    _context: Context
): Promise<void> {
    try {
        validateRequestData(requestData);
        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);
        if (!integration) {
            throw new Error("Active integration not found");
        }
        const validIntegration = await ensureValidToken(integration);
        console.log(`🚀 Fetching CreditNotes for company: ${requestData.companyId}`);
        const creditNotesData = await getCreditNotes(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );
        if (creditNotesData && creditNotesData.length > 0) {
            if (requestData.dumpToDatabase) {
                await saveCreditNotesToDatabase(creditNotesData, requestData.companyId, prisma);
                console.log(`✅ Successfully processed ${creditNotesData.length} creditnotes`);
            } else {
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No creditnotes found to process");
        }
    } catch (error) {
        console.error("Error processing creditnotes request:", error);
        throw error;
    }
}

function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });
    if (!integration) {
        throw new Error("Active integration not found for company");
    }
    return integration;
}

async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }
    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }
    return integration;
}

const getCreditNotes = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}/CreditNotes`;
        console.log(`Fetching creditnotes from Xero API: ${url}`);
        const params: any = {};
        if (requestData.where) params.where = requestData.where;
        if (requestData.page) params.page = requestData.page;
        if (requestData.pageSize) params.pageSize = requestData.pageSize;
        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                'Accept': 'application/json'
            },
            params,
        });
        console.log('Xero CreditNotes API raw response:', JSON.stringify(response.data, null, 2));
        if (response.data && response.data.CreditNotes) {
            console.log(`Retrieved ${response.data.CreditNotes.length} creditnotes from Xero`);
            return response.data.CreditNotes;
        }
        return [];
    } catch (error: any) {
        console.error("Error fetching creditnotes from Xero:", error.response?.data || error.message);
        throw new XeroError(`Failed to fetch creditnotes: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
    }
};

function parseXeroDate(dateString: string | null | undefined): Date | null {
    if (!dateString || typeof dateString !== 'string') return null;
    const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
    if (match) {
        const timestamp = parseInt(match[1] || "0");
        return new Date(timestamp);
    }
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

function mapXeroCreditNoteToPrismaCreditNote(creditNote: any, companyId: string): any {
    return {
        CreditNoteID: creditNote.CreditNoteID,
        Type: creditNote.Type || null,
        CreditNoteNumber: creditNote.CreditNoteNumber || null,
        ContactId: creditNote.Contact?.ContactID || null,
        ContactName: creditNote.Contact?.Name || null,
        ContactFirstName: creditNote.Contact?.FirstName || null,
        ContactLastName: creditNote.Contact?.LastName || null,
        Date: parseXeroDate(creditNote.Date),
        DueDate: parseXeroDate(creditNote.DueDate),
        FullyPaidOnDate: parseXeroDate(creditNote.FullyPaidOnDate),
        LineAmountType: creditNote.LineAmountType || null,
        Reference: creditNote.Reference || null,
        Status: creditNote.Status || null,
        AmountDue: creditNote.AmountDue || null,
        AmountPaid: creditNote.AmountPaid || null,
        AmountAllocated: creditNote.AmountAllocated || null,
        AmountDueToDate: creditNote.AmountDueToDate || null,
        AmountPaidToDate: creditNote.AmountPaidToDate || null,
        AmountAllocatedToDate: creditNote.AmountAllocatedToDate || null,
        RemainingCredit: creditNote.RemainingCredit || null,
        CurrencyCode: creditNote.CurrencyCode || null,
        CurrencyRate: creditNote.CurrencyRate || null,
        SubTotal: creditNote.SubTotal || null,
        TotalTax: creditNote.TotalTax || null,
        Total: creditNote.Total || null,
        UpdateUTCDate: parseXeroDate(creditNote.UpdatedDateUTC) || new Date(),
        CompanyId: companyId
    };
}

function mapXeroCreditNoteLineToPrismaCreditNoteLine(line: any, creditNote: any, companyId: string): any {
    return {
        Id: line.LineItemID || undefined,
        CreditNoteId: creditNote.CreditNoteID,
        LineItemId: line.LineItemID || null,
        CreditNoteNumber: creditNote.CreditNoteNumber || null,
        ContactId: creditNote.Contact?.ContactID || null,
        ContactName: creditNote.Contact?.Name || null,
        Date: parseXeroDate(creditNote.Date),
        Reference: creditNote.Reference || null,
        ItemCode: line.ItemCode || null,
        Description: line.Description || null,
        Quantity: line.Quantity || null,
        UnitAmount: line.UnitAmount || null,
        UnitCost: line.UnitCost || null,
        AccountCode: line.AccountCode || null,
        TaxAmount: line.TaxAmount || null,
        TaxType: line.TaxType || null,
        LineAmount: line.LineAmount || null,
        TrackingCategory1: (line.Tracking && line.Tracking[0]?.Name) || null,
        TrackingCategory1Value: (line.Tracking && line.Tracking[0]?.Option) || null,
        TrackingCategory2: (line.Tracking && line.Tracking[1]?.Name) || null,
        TrackingCategory2Value: (line.Tracking && line.Tracking[1]?.Option) || null,
        CurrencyCode: creditNote.CurrencyCode || null,
        CurrencyRate: creditNote.CurrencyRate || null,
        CompanyId: companyId
    };
}

async function saveCreditNotesToDatabase(
    creditNotes: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<void> {
    console.log(`Saving ${creditNotes.length} creditnotes to database`);

    // --- CREDIT NOTES ---
    // 1. Map and collect all credit notes
    const creditNoteRecords = creditNotes.map(cn => mapXeroCreditNoteToPrismaCreditNote(cn, companyId));
    const creditNoteIds = creditNoteRecords.map(cn => cn.CreditNoteID);

    // 2. Fetch existing credit note IDs
    const existingNotes = await prisma.creditNote.findMany({
        where: { CreditNoteID: { in: creditNoteIds } },
        select: { CreditNoteID: true }
    });
    const existingNoteIds = new Set(existingNotes.map(n => n.CreditNoteID));

    // 3. Split into new and existing
    const notesToInsert = creditNoteRecords.filter(cn => !existingNoteIds.has(cn.CreditNoteID));
    const notesToUpdate = creditNoteRecords.filter(cn => existingNoteIds.has(cn.CreditNoteID));

    // 4. Bulk insert new credit notes
    if (notesToInsert.length > 0) {
        await prisma.creditNote.createMany({ data: notesToInsert, skipDuplicates: true });
        console.log(`Inserted ${notesToInsert.length} new creditnotes`);
    }
    // 5. Bulk update existing credit notes
    if (notesToUpdate.length > 0) {
        await prisma.$transaction(
            notesToUpdate.map(note =>
                prisma.creditNote.update({
                    where: { CreditNoteID: note.CreditNoteID },
                    data: note
                })
            )
        );
        console.log(`Updated ${notesToUpdate.length} existing creditnotes`);
    }

    // --- CREDIT NOTE LINES ---
    // 1. Map and collect all credit note lines
    const allLines = creditNotes.flatMap(cn =>
        (Array.isArray(cn.LineItems) ? cn.LineItems.map((line: any) => mapXeroCreditNoteLineToPrismaCreditNoteLine(line, cn, companyId)) : [])
    );
    const lineIds = allLines.map(line => line.Id).filter(Boolean);

    // 1a. Delete extra credit note lines for these credit notes
    if (creditNoteIds.length > 0) {
        await prisma.creditNoteLine.deleteMany({
            where: {
                CreditNoteId: { in: creditNoteIds },
                Id: { notIn: lineIds },
            },
        });
        console.log(`Deleted credit note lines not present in latest Xero data for these credit notes.`);
    }

    // 2. Fetch existing credit note line IDs
    const existingLines = await prisma.creditNoteLine.findMany({
        where: { Id: { in: lineIds } },
        select: { Id: true }
    });
    const existingLineIds = new Set(existingLines.map(l => l.Id));

    // 3. Split into new and existing
    const linesToInsert = allLines.filter((line: any) => line.Id && !existingLineIds.has(line.Id));
    const linesToUpdate = allLines.filter((line: any) => line.Id && existingLineIds.has(line.Id));

    // 4. Bulk insert new credit note lines
    if (linesToInsert.length > 0) {
        await prisma.creditNoteLine.createMany({ data: linesToInsert, skipDuplicates: true });
        console.log(`Inserted ${linesToInsert.length} new creditnote lines`);
    }
    // 5. Bulk update existing credit note lines
    if (linesToUpdate.length > 0) {
        await prisma.$transaction(
            linesToUpdate.map(line =>
                prisma.creditNoteLine.update({
                    where: { Id: line.Id },
                    data: line
                })
            )
        );
        console.log(`Updated ${linesToUpdate.length} existing creditnote lines`);
    }
} 