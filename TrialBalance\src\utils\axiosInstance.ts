/**
 * Axios instance configuration for Xero API calls
 */

import axios from 'axios';
import { PRODUCTION_CONFIG } from '../config/environment';

/**
 * <PERSON>reate configured axios instance for Xero API calls
 */
const axiosInstance = axios.create({
    timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

/**
 * Request interceptor for logging
 */
axiosInstance.interceptors.request.use(
    (config) => {
        console.log(`🌐 Making ${config.method?.toUpperCase()} request to: ${config.url}`);
        return config;
    },
    (error) => {
        console.error('❌ Request interceptor error:', error);
        return Promise.reject(error);
    }
);

/**
 * Response interceptor for logging and error handling
 */
axiosInstance.interceptors.response.use(
    (response) => {
        console.log(`✅ Response received: ${response.status} ${response.statusText}`);
        return response;
    },
    (error) => {
        if (error.response) {
            console.error(`❌ API Error: ${error.response.status} ${error.response.statusText}`, {
                url: error.config?.url,
                data: error.response.data,
            });
        } else if (error.request) {
            console.error('❌ Network Error: No response received', {
                url: error.config?.url,
                timeout: error.code === 'ECONNABORTED',
            });
        } else {
            console.error('❌ Request Setup Error:', error.message);
        }
        return Promise.reject(error);
    }
);

export default axiosInstance;
