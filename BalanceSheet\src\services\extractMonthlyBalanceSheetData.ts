import { XeroReportHeader, ProcessedRowDataWithoutTracking } from '../types';

export function extractMonthlyBalanceSheetData(
    report: any,
    trackingCategories: any[],
    requestData: any,
    month: { year: number; month: number }
) {
    const rows = report.Rows;
    const results = [];

    for (const row of rows) {
        if (row.RowType === 'Section' && row.Rows) {
            for (const innerRow of row.Rows) {
                if (innerRow.RowType === 'Row' && innerRow.Cells) {
                    const firstCell = innerRow.Cells[0];
                    const accountName = firstCell?.Value || null;
                    let accountId = null;

                    if (firstCell?.Attributes) {
                        const accAttr = firstCell.Attributes.find((a: any) => a.Id === 'account');
                        accountId = accAttr?.Value || null;
                    }

                    const amountCell = innerRow.Cells[1];
                    const amount = parseFloat(amountCell?.Value.replace(/,/g, '')) || 0;

                    let trackingCategory1: any = null;
                    let trackingCategory2: any = null;

                    if (innerRow.Cells.length > 2) {
                        // Assuming further cells may represent tracking
                        const trackingNames = innerRow.Cells
                            .slice(2)
                            .map((c: any) => c.Value)
                            .filter((v: string) => !!v);

                        trackingNames.forEach((name: any) => {
                            trackingCategories.forEach((category) => {
                                category.options.forEach((option: any) => {
                                    if (option.name === name) {
                                        if (!trackingCategory1) trackingCategory1 = option.id;
                                        else trackingCategory2 = option.id;
                                    }
                                });
                            });
                        });
                    }

                    if (accountId) {
                        results.push({
                            Year: month.year,
                            Month: month.month,
                            AccountId: accountId,
                            AccountName: accountName,
                            Amount: amount,
                            TrackingCategoryId1: trackingCategory1,
                            TrackingCategoryId2: trackingCategory2,
                            CompanyId: requestData.companyId,
                        });
                    }
                }
            }
        }
    }

    return results;
}

/**
 * Extract Balance Sheet data WITHOUT tracking categories (aggregated by account)
 *
 * This function processes Balance Sheet data and aggregates amounts by account,
 * ignoring tracking category dimensions for summary reporting.
 *
 * @param report - Xero Balance Sheet report data
 * @param requestData - Request data containing company information
 * @param month - Month information with year and month
 * @returns ProcessedRowDataWithoutTracking[] - Aggregated balance sheet data
 */
export function extractMonthlyBalanceSheetDataWithoutTracking(
    report: any,
    requestData: any,
    month: { year: number; month: number }
): ProcessedRowDataWithoutTracking[] {
    const rows = report.Rows;

    // Map to aggregate amounts by account
    const accountMap = new Map<string, { accountName: string; totalAmount: number }>();

    for (const row of rows) {
        if (row.RowType === 'Section' && row.Rows) {
            for (const innerRow of row.Rows) {
                if (innerRow.RowType === 'Row' && innerRow.Cells) {
                    const firstCell = innerRow.Cells[0];
                    const accountName = firstCell?.Value || null;
                    let accountId = null;

                    if (firstCell?.Attributes) {
                        const accAttr = firstCell.Attributes.find((a: any) => a.Id === 'account');
                        accountId = accAttr?.Value || null;
                    }

                    if (accountId && accountName) {
                        const amountCell = innerRow.Cells[1];
                        const amount = parseFloat(amountCell?.Value?.replace(/,/g, '')) || 0;

                        if (accountMap.has(accountId)) {
                            const existing = accountMap.get(accountId)!;
                            existing.totalAmount += amount;
                        } else {
                            accountMap.set(accountId, {
                                accountName,
                                totalAmount: amount
                            });
                        }
                    }
                }
            }
        }
    }

    // Convert map to results array
    const results: ProcessedRowDataWithoutTracking[] = [];
    for (const [accountId, data] of accountMap.entries()) {
        if (data.totalAmount !== 0) { // Only include accounts with non-zero amounts
            results.push({
                Year: month.year,
                Month: month.month,
                AccountId: accountId,
                AccountName: data.accountName,
                Amount: data.totalAmount,
                CompanyId: requestData.companyId,
            });
        }
    }

    return results;
}
