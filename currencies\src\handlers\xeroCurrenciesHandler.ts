/**
 * Xero Currencies Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Currencies data from Xero API.
 *
 * Key Features:
 * - Bulk insert and update of currencies
 * - Handles both API Gateway and SQS events
 * - Automatic Xero token refresh
 * - Comprehensive error handling and logging
 *
 * Usage:
 * - API Gateway: POST /currencies/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */
import { APIGatewayProxyEvent, Context, APIGatewayProxyResult, SQSEvent } from 'aws-lambda';
import { PrismaClient, Company, Account } from '@prisma/client';

import axios from '../utils/axiosInstance';
import { refreshXeroToken } from '../services/refreshTokenService';
import { getXeroConfig } from '../config/environment';
import {
    XeroRequestData,
    ValidationError,
    XeroError,
} from '../types';

import dotenv from 'dotenv';
dotenv.config();

// Prisma
let prisma: PrismaClient | null = null;
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

// Lambda handler
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    if ("Records" in event && event.Records?.length) {
        // SQS
        for (const record of event.Records) {
            try {
                const requestData = JSON.parse(record.body);
                await processRequest(requestData, context);
            } catch (err) {
                console.error("Failed to process SQS message", err);
                throw err; // let Lambda/SQS retry
            }
        }
        return;
    } else {
        // API Gateway
        try {
            const requestData = parseRequestData(event);
            await processRequest(requestData, context);

            return {
                statusCode: 200,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: true,
                    message: "Currencies data processed successfully",
                    timestamp: new Date().toISOString(),
                }),
            };
        } catch (err: any) {
            console.error("Handler error:", err);
            return {
                statusCode: err instanceof ValidationError ? 400 : 500,
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    success: false,
                    error: err.message,
                    type: err.constructor.name,
                }),
            };
        }
    }
};

// parse event
export function parseRequestData(
    event: APIGatewayProxyEvent | SQSEvent | { body?: string }
): XeroRequestData {
    // handle SQS
    if ("Records" in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse SQS message body");
        }
    }

    // handle API Gateway
    if ("body" in event && event.body) {
        try {
            const parsed =
                typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            return parsed as XeroRequestData;
        } catch {
            throw new ValidationError("Failed to parse API Gateway body");
        }
    }

    throw new ValidationError("Invalid request data");
}

// process request
async function processRequest(
    requestData: XeroRequestData,
    _context: Context
): Promise<void> {
    try {
        validateRequestData(requestData);

        const prisma = getPrismaClient();
        const integration = await getActiveIntegration(requestData, prisma);

        if (!integration) {
            throw new Error("Active integration not found");
        }

        const validIntegration = await ensureValidToken(integration);

        console.log(`🚀 Fetching Currencies for company: ${requestData.companyId}`);

        // Fetch currencies from Xero
        const currenciesData = await getCurrencies(
            validIntegration.XeroAccessToken!,
            validIntegration.XeroTenantId!,
            requestData
        );

        if (currenciesData && currenciesData.length > 0) {
            if (requestData.dumpToDatabase) {
                await saveCurrenciesToDatabase(currenciesData, requestData.companyId, prisma);
                console.log(`✅ Successfully processed ${currenciesData.length} currencies`);
            } else {
                console.log("Data fetched but not dumped to DB (dumpToDatabase is false)");
            }
        } else {
            console.log("No currencies found to process");
        }

    } catch (error) {
        console.error("Error processing currencies request:", error);
        throw error;
    }
}

// Validate request data
function validateRequestData(data: XeroRequestData): void {
    if (!data.companyId) {
        throw new ValidationError("companyId is required");
    }
}

// Get active integration
async function getActiveIntegration(data: XeroRequestData, prisma: PrismaClient): Promise<Company> {
    const integration = await prisma.company.findFirst({
        where: {
            Id: data.companyId,
            ConnectionStatus: "ACTIVE",
        },
    });

    if (!integration) {
        throw new Error("Active integration not found for company");
    }

    return integration;
}

// Ensure valid token
async function ensureValidToken(integration: Company): Promise<Company> {
    if (!integration.XeroAccessToken || !integration.XeroTokenExpiry) {
        throw new Error("No access token found");
    }

    const now = new Date();
    const expiry = new Date(integration.XeroTokenExpiry);
    // Add a 10 minute buffer (600,000 ms)
    if (expiry.getTime() - now.getTime() <= 10 * 60 * 1000) {
        console.log("Token expired or about to expire, refreshing...");
        return await refreshXeroToken(integration);
    }

    return integration;
}

// Get currencies from Xero
const getCurrencies = async (
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<any[]> => {
    try {
        const config = getXeroConfig();
        const url = `${config.baseUrl}/Currencies`;
        
        console.log(`Fetching currencies from Xero API: ${url}`);

        // Build query params
        const params: any = {};
        if (requestData.where) params.where = requestData.where;
        if (requestData.page) params.page = requestData.page;
        if (requestData.pageSize) params.pageSize = requestData.pageSize;

        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                'Accept': 'application/json'
            },
            params,
        });

        // Log the full Xero response for debugging
        console.log('Xero Currencies API raw response:', JSON.stringify(response.data, null, 2));

        if (response.data && response.data.Currencies) {
            console.log(`Retrieved ${response.data.Currencies.length} currencies from Xero`);
            return response.data.Currencies;
        }

        return [];
    } catch (error: any) {
        console.error("Error fetching currencies from Xero:", error.response?.data || error.message);
        throw new XeroError(`Failed to fetch currencies: ${error.response?.data?.Elements?.[0]?.ValidationErrors?.[0]?.Message || error.message}`);
    }
};

// Helper function to parse Xero's .NET date format
function parseXeroDate(dateString: string | null | undefined): Date | null {
    if (!dateString || typeof dateString !== 'string') return null;
    
    // Handle Xero's .NET date format: "/Date(1751362260967+0000)/"
    const match = dateString.match(/\/Date\((\d+)([+-]\d{4})\)\//);
    if (match) {
        const timestamp = parseInt(match[1] || "0");
        return new Date(timestamp);
    }
    
    // Try parsing as regular date string
    const parsedDate = new Date(dateString);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
}

// Map Xero currency to Prisma currency data
function mapXeroCurrencyToPrismaCurrency(currency: any, companyId: string): any {
    return {
        Code: currency.Code || null,
        Description: currency.Description || null,
        UpdateUTCDate: parseXeroDate(currency.UpdatedDateUTC)?.toISOString() || new Date().toISOString(),
        CompanyId: companyId
    };
}

// Save currencies to database
async function saveCurrenciesToDatabase(
    currencies: any[],
    companyId: string,
    prisma: PrismaClient
): Promise<void> {
    console.log(`Saving ${currencies.length} currencies to database`);

    // 1. Map all currencies to DB shape
    const currencyRecords = currencies.map(currency => mapXeroCurrencyToPrismaCurrency(currency, companyId));
    const codes = currencyRecords.map(c => c.Code);

    // 2. Fetch existing Codes
    const existingCurrencies = await prisma.currency.findMany({
        where: { Code: { in: codes } },
        select: { Code: true }
    });
    const existingCodes = new Set(existingCurrencies.map(c => c.Code));

    // 3. Split into new and existing
    const currenciesToInsert = currencyRecords.filter(c => !existingCodes.has(c.Code));
    const currenciesToUpdate = currencyRecords.filter(c => existingCodes.has(c.Code));

    // 4. Bulk insert new currencies
    if (currenciesToInsert.length > 0) {
        await prisma.currency.createMany({ data: currenciesToInsert, skipDuplicates: true });
        console.log(`Inserted ${currenciesToInsert.length} new currencies`);
    }
    // 5. Bulk update existing currencies
    if (currenciesToUpdate.length > 0) {
        await prisma.$transaction(
            currenciesToUpdate.map(currency =>
                prisma.currency.update({
                    where: { Code: currency.Code },
                    data: currency
                })
            )
        );
        console.log(`Updated ${currenciesToUpdate.length} existing currencies`);
    }
}
